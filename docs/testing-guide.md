# 测试指南

## 概述

本项目使用 Jest 作为测试框架，支持单元测试和集成测试。测试文件位于 `src/test/` 和 `src/services/__tests__/` 目录中。

## 测试环境配置

### 1. 依赖安装

确保已安装所有测试依赖：

```bash
npm install
```

### 2. 数据库设置

测试使用 SQLite 数据库，需要先生成 Prisma 客户端：

```bash
# 生成 Prisma 客户端
npm run db:generate

# 运行数据库迁移
npm run db:migrate
```

### 3. 环境变量

测试环境变量在 `src/test/setup.ts` 中自动配置，包括：
- `NODE_ENV=test`
- `LOG_LEVEL=error`
- 测试数据库 URL
- JWT 密钥

## 运行测试

### 基本命令

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- --testPathPattern=basic.test.ts

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试（开发时使用）
npm run test:watch
```

### 运行特定类型的测试

```bash
# 运行基础功能测试
npm test -- --testPathPattern=basic.test.ts

# 运行 OAuth 相关测试
npm test -- --testPathPattern=oauth.test.ts

# 运行非标准应用测试
npm test -- --testPathPattern=non-standard-app.test.ts

# 运行服务层测试
npm test -- --testPathPattern=auth.service.test.ts
```

## 测试文件结构

```
src/
├── test/                          # 集成测试
│   ├── setup.ts                   # 测试环境设置
│   ├── basic.test.ts              # 基础功能测试
│   ├── oauth.test.ts              # OAuth 功能测试
│   ├── oauth-integration.test.ts  # OAuth 集成测试
│   ├── oauth-performance.test.ts  # OAuth 性能测试
│   └── non-standard-app.test.ts   # 非标准应用测试
└── services/
    └── __tests__/                 # 单元测试
        └── auth.service.test.ts   # 认证服务测试
```

## 测试类型说明

### 1. 基础功能测试 (basic.test.ts)
- 测试应用的基本数学运算和字符串处理
- 验证测试环境是否正常工作

### 2. OAuth 功能测试 (oauth.test.ts)
- 测试 OAuth 提供商列表
- 测试 OAuth 重定向端点
- 测试 OAuth 回调处理
- 测试联合身份管理

### 3. 非标准应用测试 (non-standard-app.test.ts)
- 测试非标准应用的认证流程
- 测试协议适配器功能
- 测试插件管理系统

### 4. 服务层测试 (auth.service.test.ts)
- 测试认证服务的核心功能
- 测试用户注册、登录逻辑
- 使用 Mock 进行单元测试

## 测试最佳实践

### 1. 测试命名规范
- 使用中文描述测试用例
- 格式：`应该 + 预期行为`
- 示例：`应该能够执行基本的数学运算`

### 2. 测试结构
```typescript
describe('功能模块名称', () => {
  beforeAll(async () => {
    // 测试前的全局设置
  });

  afterAll(async () => {
    // 测试后的全局清理
  });

  beforeEach(async () => {
    // 每个测试前的设置
  });

  afterEach(async () => {
    // 每个测试后的清理
  });

  describe('子功能', () => {
    it('应该能够执行特定操作', async () => {
      // Arrange - 准备测试数据
      const testData = { /* ... */ };

      // Act - 执行被测试的操作
      const result = await someFunction(testData);

      // Assert - 验证结果
      expect(result).toBe(expectedValue);
    });
  });
});
```

### 3. 数据库测试
- 在 `beforeAll` 中创建测试数据
- 在 `afterAll` 中清理测试数据
- 使用事务确保测试隔离

### 4. Mock 使用
- 对外部依赖进行 Mock
- 使用 `jest.mock()` 模拟模块
- 在测试中验证 Mock 调用

## 常见问题解决

### 1. 模块路径解析错误
如果遇到 `Cannot find module '@/config'` 错误：
- 检查 `jest.config.js` 中的 `moduleNameMapping` 配置
- 确保路径映射正确

### 2. TypeScript 类型错误
- 使用 `process.env['VARIABLE_NAME']` 访问环境变量
- 使用 `response.headers['location']` 访问响应头
- 使用可选链操作符 `?.` 处理可能为 undefined 的值

### 3. 数据库连接问题
- 确保已运行 `npm run db:generate`
- 检查数据库文件权限
- 验证 DATABASE_URL 配置

### 4. 测试超时
- 增加 `testTimeout` 配置
- 使用 `jest.setTimeout()` 设置单个测试超时

## 持续集成

在 CI/CD 流水线中运行测试：

```bash
# 安装依赖
npm ci

# 生成 Prisma 客户端
npm run db:generate

# 运行测试
npm test

# 生成覆盖率报告
npm run test:coverage
```

## 调试测试

### 1. 使用 VS Code 调试
- 在测试文件中设置断点
- 使用 "Jest: Debug" 配置运行调试

### 2. 查看详细输出
```bash
# 显示详细测试输出
npm test -- --verbose

# 显示失败的测试详情
npm test -- --verbose --no-coverage
```

### 3. 运行单个测试
```bash
# 运行特定的测试用例
npm test -- --testNamePattern="应该能够执行基本的数学运算"
```

## 测试覆盖率

查看测试覆盖率报告：

```bash
# 生成覆盖率报告
npm run test:coverage

# 查看 HTML 报告
open coverage/lcov-report/index.html
```

覆盖率目标：
- 语句覆盖率：> 80%
- 分支覆盖率：> 75%
- 函数覆盖率：> 80%
- 行覆盖率：> 80%
