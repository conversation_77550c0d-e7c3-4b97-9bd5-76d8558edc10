# 身份提供商 (IdP) - 公共 API 文档

| 版本 | 日期       | 作者 | 变更说明 |
| :--- | :--------- | :--- | :------- |
| 1.0  | 2025-07-26 | Gemini | 初始草案 |

## 1.0 概述

本文档定义了身份提供商 (IdP) 的公共 RESTful API。这些 API 旨在供前端界面、移动应用以及需要以编程方式与 IdP 交互的后端服务使用。所有 API 端点都应遵循标准的 HTTP 状态码，并返回 JSON 格式的响应。

API 的根路径为 `/api/v1`。

## 2.0 认证 (Authentication)

这些端点处理用户注册、登录、注销和密码管理。

- `POST /auth/register`
  - **描述:** 使用邮箱/手机号和密码注册一个新用户。
  - **请求体:** `{ "email": "<EMAIL>", "password": "strongpassword123" }`
  - **成功响应 (201):** `{ "userId": "uuid-1234", "status": "pending_verification" }`

- `POST /auth/login`
  - **描述:** 使用凭据进行身份验证。
  - **请求体:** `{ "username": "<EMAIL>", "password": "strongpassword123" }`
  - **成功响应 (200):** `{ "accessToken": "jwt_access_token", "refreshToken": "jwt_refresh_token" }`

- `POST /auth/logout`
  - **描述:** 使用当前的 `refreshToken` 使会话无效。
  - **请求体:** `{ "refreshToken": "jwt_refresh_token" }`
  - **成功响应 (204):** No Content

- `POST /auth/refresh-token`
  - **描述:** 使用 `refreshToken` 获取一个新的 `accessToken`。
  - **请求体:** `{ "refreshToken": "jwt_refresh_token" }`
  - **成功响应 (200):** `{ "accessToken": "new_jwt_access_token" }`

- `POST /auth/forgot-password`
  - **描述:** 启动密码重置流程。
  - **请求体:** `{ "email": "<EMAIL>" }`
  - **成功响应 (202):** Accepted

- `POST /auth/reset-password`
  - **描述:** 使用从邮件/短信中获取的令牌来设置新密码。
  - **请求体:** `{ "token": "reset_token", "newPassword": "newstrongpassword456" }`
  - **成功响应 (204):** No Content

## 3.0 联合认证 (Federated Authentication)

- `GET /auth/providers`
  - **描述:** 获取系统配置的所有可用第三方身份源列表。
  - **成功响应 (200):** `[{ "id": "google", "name": "Google" }, { "id": "github", "name": "GitHub" }]`

- `GET /auth/{provider}/login`
  - **描述:** 将用户重定向到第三方身份提供商进行认证。`{provider}` 是 `google`, `github` 等。
  - **成功响应 (302):** Redirect

- `GET /auth/{provider}/callback`
  - **描述:** 处理来自第三方身份提供商的回调，完成登录或账户关联流程。
  - **成功响应 (200):** `{ "accessToken": "jwt_access_token", "refreshToken": "jwt_refresh_token" }`

## 4.0 用户个人资料 (Current User - /me)

所有这些端点都需要有效的 `accessToken`。

- `GET /me`
  - **描述:** 获取当前认证用户的个人资料。
  - **成功响应 (200):** `{ "userId": "uuid-1234", "email": "<EMAIL>", "nickname": "John" }`

- `PUT /me`
  - **描述:** 更新当前用户的个人资料。
  - **请求体:** `{ "nickname": "Johnny" }`
  - **成功响应 (200):** `{ "userId": "uuid-1234", "email": "<EMAIL>", "nickname": "Johnny" }`

- `POST /me/change-password`
  - **描述:** 修改当前用户的密码。
  - **请求体:** `{ "currentPassword": "old_password", "newPassword": "new_password" }`
  - **成功响应 (204):** No Content

- `GET /me/mfa`
  - **描述:** 获取用户的 MFA 配置状态。
  - **成功响应 (200):** `{ "isEnabled": true, "methods": ["totp"] }`

- `POST /me/mfa/enable`
  - **描述:** 为用户启用 MFA。
  - **请求体:** `{ "method": "totp" }`
  - **成功响应 (200):** `{ "setupKey": "...", "qrCodeUri": "..." }` (用于 TOTP 设置)

- `POST /me/mfa/verify`
  - **描述:** 验证并完成 MFA 设置。
  - **请求体:** `{ "method": "totp", "code": "123456" }`
  - **成功响应 (204):** No Content

- `GET /me/connections`
  - **描述:** 列出用户已关联的所有第三方账号。
  - **成功响应 (200):** `[{ "connectionId": "conn-abc", "provider": "google", "identity": "<EMAIL>" }]`

- `DELETE /me/connections/{connectionId}`
  - **描述:** 解除与第三方账号的关联。
  - **成功响应 (204):** No Content

## 5.0 管理 API (/admin)

这些端点需要系统管理员权限。

### 5.1 用户管理

- `GET /admin/users`
  - **描述:** 获取用户列表，支持分页和过滤。
  - **成功响应 (200):** `{ "data": [...], "total": 100 }`

- `POST /admin/users`
  - **描述:** 创建一个新用户。
  - **成功响应 (201):** `{ "userId": "uuid-5678", ... }`

- `GET /admin/users/{userId}`
  - **描述:** 获取特定用户的详细信息。
  - **成功响应 (200):** `{ "userId": "uuid-5678", ... }`

- `PUT /admin/users/{userId}`
  - **描述:** 更新用户信息。
  - **成功响应 (200):** `{ "userId": "uuid-5678", ... }`

- `DELETE /admin/users/{userId}`
  - **描述:** 删除一个用户。
  - **成功响应 (204):** No Content

### 5.2 应用管理

- `GET /admin/apps`
  - **描述:** 获取所有已注册的应用列表。
  - **成功响应 (200):** `[{ "appId": "app-1", "name": "My App", ... }]`

- `POST /admin/apps`
  - **描述:** 注册一个新的应用。
  - **成功响应 (201):** `{ "appId": "app-2", "name": "New App", "clientId": "...", "clientSecret": "..." }`

- `GET /admin/apps/{appId}`
  - **描述:** 获取特定应用的详细信息。
  - **成功响应 (200):** `{ "appId": "app-1", ... }`

- `PUT /admin/apps/{appId}`
  - **描述:** 更新一个应用的信息。
  - **成功响应 (200):** `{ "appId": "app-1", ... }`

- `DELETE /admin/apps/{appId}`
  - **描述:** 删除一个应用。
  - **成功响应 (204):** No Content

### 5.3 系统配置

- `GET /admin/config`
  - **描述:** 获取系统级配置。
  - **成功响应 (200):** `{ "passwordPolicy": { ... }, "zeroTrust": { "enabled": false } }`

- `PUT /admin/config`
  - **描述:** 更新系统级配置。
  - **成功响应 (200):** `{ ... }`

## 6.0 SSO 协议端点

这些端点遵循 OIDC 和 SAML 2.0 标准，用于与服务提供商 (SP) 集成。

- `GET /oauth2/authorize` (OIDC/OAuth2)
- `POST /oauth2/token` (OIDC/OAuth2)
- `GET /userinfo` (OIDC)
- `GET /.well-known/openid-configuration` (OIDC Discovery)
- `GET /saml2/sso` (SAML)
- `POST /saml2/sso` (SAML)
- `GET /saml2/metadata` (SAML)
