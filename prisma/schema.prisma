// Prisma schema file for IdP (Identity Provider)
// 身份提供商数据库模式定义

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表 - 核心用户信息
model User {
  id                String    @id @default(uuid())
  email             String    @unique
  phone             String?   @unique
  username          String?   @unique
  passwordHash      String?   // 可为空，支持仅第三方登录的用户
  nickname          String?
  firstName         String?
  lastName          String?
  avatar            String?
  emailVerified     Boolean   @default(false)
  phoneVerified     Boolean   @default(false)
  isActive          Boolean   @default(true)
  isLocked          Boolean   @default(false)
  lockReason        String?
  lastLoginAt       DateTime?
  lastLoginIp       String?
  passwordChangedAt DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // 关联关系
  sessions          Session[]
  mfaDevices        MFADevice[]
  federatedIdentities FederatedIdentity[]
  auditLogs         AuditLog[]
  riskAssessments   RiskAssessment[]
  userRoles         UserRole[]
  refreshTokens     RefreshToken[]

  @@map("users")
}

// 应用表 - 注册的服务提供商
model Application {
  id                String    @id @default(uuid())
  name              String
  description       String?
  clientId          String    @unique
  clientSecret      String
  redirectUris      Json      // JSON数组存储多个回调URL
  allowedOrigins    Json      // 允许的CORS源
  logoUrl           String?
  homepageUrl       String?
  privacyPolicyUrl  String?
  termsOfServiceUrl String?

  // SSO协议配置
  supportedProtocols Json     // ["oidc", "oauth2", "saml", "custom-oauth", "api-key", "webhook"]
  oidcConfig        Json?     // OIDC特定配置
  samlConfig        Json?     // SAML特定配置
  customProtocolConfig Json?  // 自定义协议配置

  // 非标准应用支持
  appType           String    @default("standard") // "standard", "legacy", "custom", "api_only", "webhook", "mobile", "iot"
  customAuthFlow    Json?     // 自定义认证流程配置

  // 钩子函数配置
  preAuthHook       String?   // 预认证钩子函数
  postAuthHook      String?   // 后认证钩子函数
  tokenTransformHook String?  // 令牌转换钩子函数
  userTransformHook String?   // 用户信息转换钩子函数

  // Webhook配置
  webhookUrls       Json?     // Webhook URL配置 {"onSuccess": "url", "onError": "url", "onLogout": "url"}
  webhookSecret     String?   // Webhook签名密钥
  webhookTimeout    Int?      @default(30) // Webhook超时时间（秒）

  // API配置
  apiKeyEnabled     Boolean   @default(false)
  apiKeyConfig      Json?     // API密钥配置
  rateLimitConfig   Json?     // 速率限制配置

  // 安全配置
  requireMfa        Boolean   @default(false)
  zeroTrustEnabled  Boolean   @default(false)
  allowedIpRanges   Json?     // 允许的IP范围
  tokenLifetime     Int?      @default(3600) // 令牌生命周期（秒）

  // 扩展配置
  customSettings    Json?     // 自定义设置
  pluginConfig      Json?     // 插件配置

  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // 关联关系
  sessions          Session[]
  auditLogs         AuditLog[]
  protocolConfigs   ApplicationProtocolConfig[]
  apiKeys           ApiKey[]

  @@map("applications")
}

// 会话表 - 用户登录会话
model Session {
  id              String    @id @default(uuid())
  userId          String
  applicationId   String?   // 可为空，表示IdP自身的会话
  sessionToken    String    @unique
  deviceId        String?   // 设备标识
  deviceInfo      Json?     // 设备信息
  ipAddress       String
  userAgent       String?
  location        Json?     // 地理位置信息
  
  // 会话状态
  isActive        Boolean   @default(true)
  expiresAt       DateTime
  lastAccessedAt  DateTime  @default(now())
  
  // 认证信息
  authMethod      String    // "password", "mfa", "federated"
  mfaVerified     Boolean   @default(false)
  riskScore       Int?      // 风险评分 0-100
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // 关联关系
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  application     Application? @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

// MFA设备表 - 多因素认证设备
model MFADevice {
  id            String    @id @default(uuid())
  userId        String
  type          String    // "totp", "email", "sms"
  name          String    // 用户自定义设备名称
  secret        String?   // TOTP密钥（加密存储）
  backupCodes   Json?     // 备用恢复码（加密存储）
  phoneNumber   String?   // 短信MFA的手机号
  emailAddress  String?   // 邮件MFA的邮箱
  
  isActive      Boolean   @default(true)
  isVerified    Boolean   @default(false)
  lastUsedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("mfa_devices")
}

// 联合身份表 - 第三方账户关联
model FederatedIdentity {
  id            String    @id @default(uuid())
  userId        String
  provider      String    // "google", "github", "microsoft"
  providerId    String    // 第三方平台的用户ID
  email         String?   // 第三方账户邮箱
  name          String?   // 第三方账户名称
  avatar        String?   // 第三方账户头像
  accessToken   String?   // 访问令牌（加密存储）
  refreshToken  String?   // 刷新令牌（加密存储）
  expiresAt     DateTime?
  
  isActive      Boolean   @default(true)
  lastUsedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([provider, providerId])
  @@map("federated_identities")
}

// 审计日志表 - 安全和操作日志
model AuditLog {
  id            String    @id @default(uuid())
  userId        String?   // 可为空，系统操作
  applicationId String?   // 可为空，IdP自身操作
  action        String    // 操作类型
  resource      String    // 操作资源
  details       Json?     // 详细信息
  ipAddress     String?
  userAgent     String?
  success       Boolean
  errorMessage  String?
  
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  application   Application? @relation(fields: [applicationId], references: [id], onDelete: SetNull)
  
  @@map("audit_logs")
}

// 风险评估表 - 零信任模式风险评估
model RiskAssessment {
  id            String    @id @default(uuid())
  userId        String
  sessionId     String?
  
  // 风险因子
  ipRisk        Int       @default(0)    // IP风险评分
  deviceRisk    Int       @default(0)    // 设备风险评分
  behaviorRisk  Int       @default(0)    // 行为风险评分
  locationRisk  Int       @default(0)    // 位置风险评分
  timeRisk      Int       @default(0)    // 时间风险评分
  
  totalRisk     Int       @default(0)    // 总风险评分
  riskLevel     String    // "low", "medium", "high", "critical"
  
  // 评估结果
  action        String    // "allow", "mfa_required", "deny"
  reason        String?   // 风险原因
  
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("risk_assessments")
}

// 角色表 - 用户角色管理
model Role {
  id            String    @id @default(uuid())
  name          String    @unique
  description   String?
  permissions   Json?     // 权限列表
  
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  userRoles     UserRole[]
  
  @@map("roles")
}

// 用户角色关联表
model UserRole {
  id        String    @id @default(uuid())
  userId    String
  roleId    String
  
  createdAt DateTime  @default(now())
  
  // 关联关系
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  role      Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  
  @@unique([userId, roleId])
  @@map("user_roles")
}

// 刷新令牌表 - JWT刷新令牌管理
model RefreshToken {
  id            String    @id @default(uuid())
  userId        String
  token         String    @unique
  deviceId      String?
  
  isActive      Boolean   @default(true)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

// 应用协议配置表 - 应用特定的协议配置
model ApplicationProtocolConfig {
  id              String    @id @default(uuid())
  applicationId   String
  protocolName    String    // "oidc", "saml", "custom-oauth", etc.
  protocolVersion String    @default("1.0")
  config          Json      // 协议特定配置
  isActive        Boolean   @default(true)

  // 自定义处理器
  customHandlers  Json?     // {"preAuth": "handler_name", "postAuth": "handler_name"}

  // Webhook配置
  webhooks        Json?     // {"onSuccess": "url", "onError": "url"}

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // 关联关系
  application     Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@unique([applicationId, protocolName])
  @@map("application_protocol_configs")
}

// 插件表 - 管理认证插件
model Plugin {
  id            String    @id @default(uuid())
  name          String    @unique
  version       String
  description   String?
  author        String?

  // 插件文件信息
  filePath      String    // 插件文件路径
  entryPoint    String    // 入口点函数
  checksum      String    // 文件校验和

  // 插件配置
  config        Json?     // 插件配置
  dependencies  Json?     // 依赖的其他插件

  // 插件状态
  isEnabled     Boolean   @default(false)
  isLoaded      Boolean   @default(false)
  loadError     String?   // 加载错误信息

  // 插件提供的功能
  protocolAdapters Json?    // 提供的协议适配器
  customHandlers   Json?    // 提供的自定义处理器

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("plugins")
}

// 自定义认证流程表
model CustomAuthFlow {
  id            String    @id @default(uuid())
  name          String    @unique
  description   String?

  // 流程定义
  steps         Json      // 认证流程步骤定义
  config        Json?     // 流程配置

  // 使用统计
  usageCount    Int       @default(0)
  lastUsedAt    DateTime?

  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("custom_auth_flows")
}

// API密钥表 - 管理应用API密钥
model ApiKey {
  id            String    @id @default(uuid())
  applicationId String
  name          String    // 密钥名称
  keyHash       String    @unique // 密钥哈希值
  keyPrefix     String    // 密钥前缀（用于识别）

  // 权限配置
  scopes        Json?     // 权限范围
  allowedIps    Json?     // 允许的IP地址

  // 使用统计
  usageCount    Int       @default(0)
  lastUsedAt    DateTime?

  // 状态
  isActive      Boolean   @default(true)
  expiresAt     DateTime?

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关联关系
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// 系统配置表 - 全局配置
model SystemConfig {
  id            String    @id @default(uuid())
  key           String    @unique
  value         Json
  description   String?

  updatedAt     DateTime  @updatedAt

  @@map("system_configs")
}
