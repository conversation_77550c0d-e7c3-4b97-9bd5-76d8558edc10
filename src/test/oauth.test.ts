/**
 * OAuth功能测试
 */

import request from 'supertest';
import { app } from '../index';
import { prisma } from '../config/database';
import { OAuthService } from '../services/oauth.service';
import { FederatedIdentityService } from '../services/federated-identity.service';
import { generateTokenPair } from '../utils/jwt';

describe('OAuth API', () => {
  beforeAll(async () => {
    // 测试前清理数据库
    await prisma.federatedIdentity.deleteMany();
    await prisma.user.deleteMany();
  });

  afterAll(async () => {
    // 测试后清理数据库
    await prisma.federatedIdentity.deleteMany();
    await prisma.user.deleteMany();
    await prisma.$disconnect();
  });

  describe('GET /api/v1/auth/providers', () => {
    it('应该返回支持的OAuth提供商列表', async () => {
      const response = await request(app)
        .get('/api/v1/auth/providers')
        .expect(200);

      expect(response.body).toHaveProperty('providers');
      expect(response.body).toHaveProperty('total');
      expect(Array.isArray(response.body.providers)).toBe(true);
    });
  });

  describe('OAuth重定向端点', () => {
    it('GET /api/v1/auth/google 应该重定向到Google OAuth', async () => {
      const response = await request(app)
        .get('/api/v1/auth/google')
        .expect(302);

      expect(response.headers.location).toContain('accounts.google.com');
    });

    it('GET /api/v1/auth/github 应该重定向到GitHub OAuth', async () => {
      const response = await request(app)
        .get('/api/v1/auth/github')
        .expect(302);

      expect(response.headers.location).toContain('github.com');
    });

    it('GET /api/v1/auth/wechat 应该重定向到微信OAuth', async () => {
      const response = await request(app)
        .get('/api/v1/auth/wechat')
        .expect(302);

      expect(response.headers.location).toContain('open.weixin.qq.com');
    });

    it('GET /api/v1/auth/weibo 应该重定向到微博OAuth', async () => {
      const response = await request(app)
        .get('/api/v1/auth/weibo')
        .expect(302);

      expect(response.headers.location).toContain('api.weibo.com');
    });
  });

  describe('OAuth回调端点', () => {
    it('Google回调应该处理错误参数', async () => {
      const response = await request(app)
        .get('/api/v1/auth/google/callback?error=access_denied')
        .expect(302);

      expect(response.headers.location).toContain('error=oauth_error');
    });

    it('GitHub回调应该处理错误参数', async () => {
      const response = await request(app)
        .get('/api/v1/auth/github/callback?error=access_denied')
        .expect(302);

      expect(response.headers.location).toContain('error=oauth_error');
    });
  });

  describe('解除OAuth关联', () => {
    it('未认证用户不能解除关联', async () => {
      const response = await request(app)
        .delete('/api/v1/auth/disconnect/google')
        .expect(401);

      expect(response.body.error).toBe('unauthorized');
    });

    it('缺少提供商参数应该返回400', async () => {
      // 这里需要模拟认证用户，实际测试中需要先登录获取token
      const response = await request(app)
        .delete('/api/v1/auth/disconnect/')
        .expect(404); // 路由不匹配
    });
  });
});

describe('OAuth服务测试', () => {
  let oauthService: OAuthService;
  let testUser: any;

  beforeAll(async () => {
    oauthService = new OAuthService();

    // 创建测试用户
    testUser = await prisma.user.create({
      data: {
        id: 'test-user-id',
        email: '<EMAIL>',
        nickname: 'Test User',
        hashedPassword: 'hashed-password',
        isEmailVerified: true
      }
    });
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.federatedIdentity.deleteMany({ where: { userId: testUser.id } });
    await prisma.user.delete({ where: { id: testUser.id } });
  });

  describe('handleOAuthLogin', () => {
    it('应该为新用户创建账户和联合身份', async () => {
      const mockProfile = {
        id: 'google-123',
        provider: 'google',
        email: '<EMAIL>',
        name: 'New User',
        avatar: 'https://example.com/avatar.jpg'
      };

      const result = await oauthService.handleOAuthLogin(mockProfile, '127.0.0.1', 'test-agent');

      expect(result.isNewUser).toBe(true);
      expect(result.federatedIdentity.isNewConnection).toBe(true);
      expect(result.user.email).toBe(mockProfile.email);
      expect(result.tokens.accessToken).toBeDefined();

      // 清理
      await prisma.federatedIdentity.deleteMany({ where: { providerId: mockProfile.id } });
      await prisma.user.delete({ where: { id: result.user.id } });
    });

    it('应该为现有用户创建联合身份关联', async () => {
      const mockProfile = {
        id: 'google-456',
        provider: 'google',
        email: testUser.email,
        name: 'Test User',
        avatar: 'https://example.com/avatar.jpg'
      };

      const result = await oauthService.handleOAuthLogin(mockProfile, '127.0.0.1', 'test-agent');

      expect(result.isNewUser).toBe(false);
      expect(result.federatedIdentity.isNewConnection).toBe(true);
      expect(result.user.id).toBe(testUser.id);

      // 清理
      await prisma.federatedIdentity.deleteMany({ where: { providerId: mockProfile.id } });
    });
  });
});

describe('联合身份服务测试', () => {
  let federatedIdentityService: FederatedIdentityService;
  let testUser: any;
  let testFederatedIdentity: any;

  beforeAll(async () => {
    federatedIdentityService = new FederatedIdentityService();

    // 创建测试用户
    testUser = await prisma.user.create({
      data: {
        id: 'test-user-fed-id',
        email: '<EMAIL>',
        nickname: 'Fed Test User',
        hashedPassword: 'hashed-password',
        isEmailVerified: true
      }
    });

    // 创建测试联合身份
    testFederatedIdentity = await prisma.federatedIdentity.create({
      data: {
        id: 'test-fed-identity-id',
        userId: testUser.id,
        provider: 'google',
        providerId: 'google-test-123',
        email: testUser.email,
        name: 'Fed Test User'
      }
    });
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.federatedIdentity.deleteMany({ where: { userId: testUser.id } });
    await prisma.user.delete({ where: { id: testUser.id } });
  });

  describe('getUserFederatedIdentities', () => {
    it('应该返回用户的联合身份列表', async () => {
      const identities = await federatedIdentityService.getUserFederatedIdentities(testUser.id);

      expect(identities).toHaveLength(1);
      expect(identities[0].provider).toBe('google');
      expect(identities[0].providerId).toBe('google-test-123');
    });
  });

  describe('getFederatedIdentityStats', () => {
    it('应该返回联合身份统计信息', async () => {
      const stats = await federatedIdentityService.getFederatedIdentityStats(testUser.id);

      expect(stats.totalConnections).toBe(1);
      expect(stats.activeConnections).toBe(1);
      expect(stats.providerCounts.google).toBe(1);
    });
  });

  describe('canDisconnectProvider', () => {
    it('有密码的用户应该可以解除联合身份', async () => {
      const result = await federatedIdentityService.canDisconnectProvider(
        testUser.id,
        testFederatedIdentity.id
      );

      expect(result.canDisconnect).toBe(true);
    });

    it('没有密码且只有一个联合身份的用户不能解除', async () => {
      // 创建没有密码的用户
      const userWithoutPassword = await prisma.user.create({
        data: {
          id: 'user-no-password',
          email: '<EMAIL>',
          nickname: 'No Password User',
          isEmailVerified: true
        }
      });

      const federatedIdentity = await prisma.federatedIdentity.create({
        data: {
          id: 'fed-identity-no-password',
          userId: userWithoutPassword.id,
          provider: 'google',
          providerId: 'google-no-password-123',
          email: userWithoutPassword.email,
          name: 'No Password User'
        }
      });

      const result = await federatedIdentityService.canDisconnectProvider(
        userWithoutPassword.id,
        federatedIdentity.id
      );

      expect(result.canDisconnect).toBe(false);
      expect(result.reason).toContain('唯一的登录方式');

      // 清理
      await prisma.federatedIdentity.delete({ where: { id: federatedIdentity.id } });
      await prisma.user.delete({ where: { id: userWithoutPassword.id } });
    });
  });
});
