/**
 * 测试环境设置
 * 配置Jest测试环境和数据库
 */

// 设置测试环境变量
process.env['NODE_ENV'] = 'test';
process.env['LOG_LEVEL'] = 'error';

// 测试数据库URL（如果没有设置，使用内存数据库）
if (!process.env['DATABASE_URL']) {
  process.env['DATABASE_URL'] = 'postgresql://test:test@localhost:5432/id_provider_test';
}

// 测试JWT密钥
if (!process.env['JWT_SECRET']) {
  process.env['JWT_SECRET'] = 'test-jwt-secret-key';
}

if (!process.env['JWT_REFRESH_SECRET']) {
  process.env['JWT_REFRESH_SECRET'] = 'test-jwt-refresh-secret-key';
}

// 全局测试超时
jest.setTimeout(30000);

// 测试前后钩子
beforeAll(async () => {
  // 测试开始前的设置
});

afterAll(async () => {
  // 测试结束后的清理
});

beforeEach(async () => {
  // 每个测试前的设置
});

afterEach(async () => {
  // 每个测试后的清理
});
