/**
 * 用户路由
 * 定义用户管理相关的API端点
 */

import { Router } from 'express';
import { UserController } from '@/controllers/user.controller';
import { MfaController } from '@/controllers/mfa.controller';
import { authenticateToken, rateLimitByUser } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const userController = new UserController();
const mfaController = new MfaController();

// 通用速率限制配置
const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    error: 'rate_limit_exceeded',
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 所有用户路由都需要认证
router.use(authenticateToken);

/**
 * @route GET /api/v1/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get('/', generalRateLimit, userController.getCurrentUser);

/**
 * @route PUT /api/v1/me
 * @desc 更新用户资料
 * @access Private
 */
router.put('/', 
  generalRateLimit,
  rateLimitByUser(10, 15 * 60 * 1000), // 每个用户15分钟内最多10次
  userController.updateProfile
);

/**
 * @route GET /api/v1/me/sessions
 * @desc 获取用户会话列表
 * @access Private
 */
router.get('/sessions', generalRateLimit, userController.getUserSessions);

/**
 * @route DELETE /api/v1/me/sessions/:sessionId
 * @desc 终止指定会话
 * @access Private
 */
router.delete('/sessions/:sessionId', 
  generalRateLimit,
  rateLimitByUser(20, 15 * 60 * 1000), // 每个用户15分钟内最多20次
  userController.terminateSession
);

/**
 * @route DELETE /api/v1/me/sessions
 * @desc 终止所有其他会话
 * @access Private
 */
router.delete('/sessions', 
  generalRateLimit,
  rateLimitByUser(5, 15 * 60 * 1000), // 每个用户15分钟内最多5次
  userController.terminateAllOtherSessions
);

/**
 * @route GET /api/v1/me/connections
 * @desc 获取用户的联合身份列表
 * @access Private
 */
router.get('/connections', generalRateLimit, userController.getFederatedIdentities);

/**
 * @route DELETE /api/v1/me/connections/:connectionId
 * @desc 解除联合身份关联
 * @access Private
 */
router.delete('/connections/:connectionId', 
  generalRateLimit,
  rateLimitByUser(10, 15 * 60 * 1000), // 每个用户15分钟内最多10次
  userController.disconnectFederatedIdentity
);

// MFA相关路由

/**
 * @route GET /api/v1/me/mfa
 * @desc 获取用户MFA状态
 * @access Private
 */
router.get('/mfa', generalRateLimit, mfaController.getMfaStatus);

/**
 * @route POST /api/v1/me/mfa/enable
 * @desc 启用MFA
 * @access Private
 */
router.post('/mfa/enable', 
  generalRateLimit,
  rateLimitByUser(5, 15 * 60 * 1000), // 每个用户15分钟内最多5次
  mfaController.enableMfa
);

/**
 * @route POST /api/v1/me/mfa/verify
 * @desc 验证MFA
 * @access Private
 */
router.post('/mfa/verify', 
  generalRateLimit,
  rateLimitByUser(10, 15 * 60 * 1000), // 每个用户15分钟内最多10次
  mfaController.verifyMfa
);

/**
 * @route DELETE /api/v1/me/mfa/devices/:deviceId
 * @desc 禁用MFA设备
 * @access Private
 */
router.delete('/mfa/devices/:deviceId', 
  generalRateLimit,
  rateLimitByUser(10, 15 * 60 * 1000), // 每个用户15分钟内最多10次
  mfaController.disableMfaDevice
);

/**
 * @route POST /api/v1/me/mfa/send-email-code
 * @desc 发送邮件验证码
 * @access Private
 */
router.post('/mfa/send-email-code', 
  generalRateLimit,
  rateLimitByUser(5, 15 * 60 * 1000), // 每个用户15分钟内最多5次
  mfaController.sendEmailCode
);

/**
 * @route POST /api/v1/me/mfa/send-sms-code
 * @desc 发送短信验证码
 * @access Private
 */
router.post('/mfa/send-sms-code', 
  generalRateLimit,
  rateLimitByUser(5, 15 * 60 * 1000), // 每个用户15分钟内最多5次
  mfaController.sendSmsCode
);

export default router;
