/**
 * 认证路由
 * 定义认证相关的API端点
 */

import { Router } from 'express';
import { AuthController } from '@/controllers/auth.controller';
import { authenticateToken, rateLimitByUser } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const authController = new AuthController();

// 通用速率限制配置
const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    error: 'rate_limit_exceeded',
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 严格的速率限制（用于敏感操作）
const strictRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每个IP最多5个请求
  message: {
    error: 'rate_limit_exceeded',
    message: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 登录速率限制
const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 每个IP最多10次登录尝试
  message: {
    error: 'login_rate_limit_exceeded',
    message: '登录尝试过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true // 成功的请求不计入限制
});

/**
 * @route POST /api/v1/auth/register
 * @desc 用户注册
 * @access Public
 */
router.post('/register', strictRateLimit, authController.register);

/**
 * @route POST /api/v1/auth/login
 * @desc 用户登录
 * @access Public
 */
router.post('/login', loginRateLimit, authController.login);

/**
 * @route POST /api/v1/auth/logout
 * @desc 用户登出
 * @access Private
 */
router.post('/logout', authenticateToken, authController.logout);

/**
 * @route POST /api/v1/auth/refresh-token
 * @desc 刷新访问令牌
 * @access Public
 */
router.post('/refresh-token', generalRateLimit, authController.refreshToken);

/**
 * @route POST /api/v1/auth/forgot-password
 * @desc 忘记密码
 * @access Public
 */
router.post('/forgot-password', strictRateLimit, authController.forgotPassword);

/**
 * @route POST /api/v1/auth/reset-password
 * @desc 重置密码
 * @access Public
 */
router.post('/reset-password', strictRateLimit, authController.resetPassword);

/**
 * @route POST /api/v1/auth/change-password
 * @desc 修改密码
 * @access Private
 */
router.post('/change-password', 
  authenticateToken, 
  rateLimitByUser(5, 15 * 60 * 1000), // 每个用户15分钟内最多5次
  authController.changePassword
);

/**
 * @route GET /api/v1/auth/verify-email
 * @desc 验证邮箱
 * @access Public
 */
router.get('/verify-email', generalRateLimit, authController.verifyEmail);

/**
 * @route POST /api/v1/auth/validate-token
 * @desc 验证访问令牌（供API网关使用）
 * @access Public
 */
router.post('/validate-token', generalRateLimit, authController.validateToken);

/**
 * @route GET /api/v1/auth/introspect
 * @desc 令牌内省端点（RFC 7662）
 * @access Public
 */
router.post('/introspect', generalRateLimit, authController.introspectToken);

export default router;
