/**
 * 认证服务测试
 * 测试用户注册、登录等核心功能
 */

import { AuthService } from '../auth.service';
import { prisma } from '@/config/database';

// Mock Prisma
jest.mock('@/config/database', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    role: {
      findUnique: jest.fn(),
    },
    userRole: {
      create: jest.fn(),
    },
    session: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    authService = new AuthService();
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('应该成功注册新用户', async () => {
      // Mock数据
      const registerData = {
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        nickname: 'testuser'
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        nickname: 'testuser'
      };

      const mockRole = {
        id: 'role-123',
        name: 'user'
      };

      // 设置Mock返回值
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null); // 用户不存在
      (prisma.user.create as jest.Mock).mockResolvedValue(mockUser);
      (prisma.role.findUnique as jest.Mock).mockResolvedValue(mockRole);
      (prisma.userRole.create as jest.Mock).mockResolvedValue({});

      // 执行测试
      const result = await authService.register(registerData);

      // 验证结果
      expect(result).toEqual({
        userId: 'user-123',
        status: 'pending_verification'
      });

      // 验证调用
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' }
      });
      expect(prisma.user.create).toHaveBeenCalled();
      expect(prisma.userRole.create).toHaveBeenCalled();
    });

    it('应该拒绝重复的邮箱注册', async () => {
      // Mock数据
      const registerData = {
        email: '<EMAIL>',
        password: 'StrongPassword123!'
      };

      const existingUser = {
        id: 'existing-user',
        email: '<EMAIL>'
      };

      // 设置Mock返回值
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(existingUser);

      // 执行测试并验证错误
      await expect(authService.register(registerData)).rejects.toThrow('邮箱已被注册');
    });

    it('应该拒绝弱密码', async () => {
      // Mock数据
      const registerData = {
        email: '<EMAIL>',
        password: '123' // 弱密码
      };

      // 设置Mock返回值
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // 执行测试并验证错误
      await expect(authService.register(registerData)).rejects.toThrow('密码强度不足');
    });
  });

  describe('login', () => {
    it('应该成功登录有效用户', async () => {
      // Mock数据
      const loginData = {
        username: '<EMAIL>',
        password: 'StrongPassword123!',
        ipAddress: '127.0.0.1'
      };

      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: '$2b$12$hashedpassword',
        isActive: true,
        isLocked: false,
        mfaDevices: []
      };

      const mockSession = {
        id: 'session-123',
        userId: 'user-123',
        sessionToken: 'session-token'
      };

      // Mock密码验证
      jest.doMock('@/utils/password', () => ({
        verifyPassword: jest.fn().mockResolvedValue(true)
      }));

      // 设置Mock返回值
      (prisma.user.findFirst as jest.Mock).mockResolvedValue(mockUser);
      (prisma.session.create as jest.Mock).mockResolvedValue(mockSession);
      (prisma.user.update as jest.Mock).mockResolvedValue(mockUser);

      // 执行测试
      const result = await authService.login(loginData);

      // 验证结果
      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('tokens');
      expect(result).toHaveProperty('sessionId');
      expect(result.requiresMfa).toBe(false);
    });

    it('应该拒绝不存在的用户', async () => {
      // Mock数据
      const loginData = {
        username: '<EMAIL>',
        password: 'password'
      };

      // 设置Mock返回值
      (prisma.user.findFirst as jest.Mock).mockResolvedValue(null);

      // 执行测试并验证错误
      await expect(authService.login(loginData)).rejects.toThrow('用户名或密码错误');
    });

    it('应该拒绝被锁定的账户', async () => {
      // Mock数据
      const loginData = {
        username: '<EMAIL>',
        password: 'password'
      };

      const lockedUser = {
        id: 'user-123',
        email: '<EMAIL>',
        isActive: true,
        isLocked: true,
        mfaDevices: []
      };

      // 设置Mock返回值
      (prisma.user.findFirst as jest.Mock).mockResolvedValue(lockedUser);

      // 执行测试并验证错误
      await expect(authService.login(loginData)).rejects.toThrow('账户已被锁定');
    });
  });
});
