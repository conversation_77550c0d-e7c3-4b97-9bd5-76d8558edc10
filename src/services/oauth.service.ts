/**
 * OAuth服务
 * 处理第三方OAuth登录的业务逻辑
 */

import { User, FederatedIdentity } from '@prisma/client';
import { prisma } from '@/config/database';
import { logger, logAuditEvent, logSecurityEvent } from '@/config/logger';
import { generateTokenPair, TokenPair } from '@/utils/jwt';
import { OAuthProfile } from '@/config/passport';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

/**
 * OAuth登录结果接口
 */
export interface OAuthLoginResult {
  user: {
    id: string;
    email: string;
    nickname?: string;
    avatar?: string;
    isEmailVerified: boolean;
  };
  tokens: TokenPair;
  sessionId: string;
  isNewUser: boolean;
  federatedIdentity: {
    id: string;
    provider: string;
    isNewConnection: boolean;
  };
}

/**
 * OAuth服务类
 */
export class OAuthService {
  /**
   * 处理OAuth登录
   */
  async handleOAuthLogin(
    profile: OAuthProfile,
    ipAddress?: string,
    userAgent?: string
  ): Promise<OAuthLoginResult> {
    try {
      // 查找现有的联合身份
      let federatedIdentity = await prisma.federatedIdentity.findUnique({
        where: {
          provider_providerId: {
            provider: profile.provider,
            providerId: profile.id
          }
        },
        include: {
          user: true
        }
      });

      let user: User;
      let isNewUser = false;
      let isNewConnection = false;

      if (federatedIdentity && federatedIdentity.isActive) {
        // 现有联合身份，更新信息
        user = federatedIdentity.user;
        
        // 更新联合身份信息
        federatedIdentity = await prisma.federatedIdentity.update({
          where: { id: federatedIdentity.id },
          data: {
            email: profile.email,
            name: profile.name,
            avatar: profile.avatar,
            accessToken: profile.accessToken ? this.encryptToken(profile.accessToken) : null,
            refreshToken: profile.refreshToken ? this.encryptToken(profile.refreshToken) : null,
            lastUsedAt: new Date()
          }
        });

        logger.info('OAuth登录 - 现有用户', {
          userId: user.id,
          provider: profile.provider,
          providerId: profile.id
        });
      } else {
        // 检查是否有相同邮箱的用户
        if (profile.email) {
          user = await prisma.user.findUnique({
            where: { email: profile.email }
          });
        }

        if (user) {
          // 现有用户，创建新的联合身份关联
          isNewConnection = true;
          
          federatedIdentity = await prisma.federatedIdentity.create({
            data: {
              id: uuidv4(),
              userId: user.id,
              provider: profile.provider,
              providerId: profile.id,
              email: profile.email,
              name: profile.name,
              avatar: profile.avatar,
              accessToken: profile.accessToken ? this.encryptToken(profile.accessToken) : null,
              refreshToken: profile.refreshToken ? this.encryptToken(profile.refreshToken) : null,
              lastUsedAt: new Date()
            }
          });

          logger.info('OAuth登录 - 关联现有用户', {
            userId: user.id,
            provider: profile.provider,
            providerId: profile.id
          });
        } else {
          // 新用户，创建用户和联合身份
          isNewUser = true;
          isNewConnection = true;

          user = await prisma.user.create({
            data: {
              id: uuidv4(),
              email: profile.email || `${profile.provider}_${profile.id}@oauth.local`,
              nickname: profile.name || `${profile.provider}_user`,
              firstName: profile.firstName,
              lastName: profile.lastName,
              avatar: profile.avatar,
              isEmailVerified: !!profile.email, // 如果有邮箱则认为已验证
              profile: {
                displayName: profile.name,
                avatar: profile.avatar
              }
            }
          });

          federatedIdentity = await prisma.federatedIdentity.create({
            data: {
              id: uuidv4(),
              userId: user.id,
              provider: profile.provider,
              providerId: profile.id,
              email: profile.email,
              name: profile.name,
              avatar: profile.avatar,
              accessToken: profile.accessToken ? this.encryptToken(profile.accessToken) : null,
              refreshToken: profile.refreshToken ? this.encryptToken(profile.refreshToken) : null,
              lastUsedAt: new Date()
            }
          });

          logger.info('OAuth登录 - 创建新用户', {
            userId: user.id,
            provider: profile.provider,
            providerId: profile.id
          });
        }
      }

      // 创建会话
      const sessionToken = this.generateSessionToken();
      const session = await prisma.session.create({
        data: {
          id: uuidv4(),
          userId: user.id,
          sessionToken,
          deviceInfo: {
            userAgent: userAgent || 'Unknown',
            platform: this.extractPlatform(userAgent),
            browser: this.extractBrowser(userAgent)
          },
          ipAddress: ipAddress || 'Unknown',
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天
          lastAccessedAt: new Date()
        }
      });

      // 生成JWT令牌
      const tokens = generateTokenPair({
        userId: user.id,
        email: user.email,
        sessionId: session.id
      });

      // 记录审计日志
      await logAuditEvent({
        userId: user.id,
        action: 'oauth_login',
        resource: 'user',
        details: {
          provider: profile.provider,
          providerId: profile.id,
          isNewUser,
          isNewConnection
        },
        ipAddress,
        userAgent
      });

      return {
        user: {
          id: user.id,
          email: user.email,
          nickname: user.nickname,
          avatar: user.avatar,
          isEmailVerified: user.isEmailVerified
        },
        tokens,
        sessionId: session.id,
        isNewUser,
        federatedIdentity: {
          id: federatedIdentity.id,
          provider: profile.provider,
          isNewConnection
        }
      };

    } catch (error) {
      logger.error('OAuth登录处理失败', {
        error,
        provider: profile.provider,
        providerId: profile.id
      });
      throw new Error('OAuth登录失败');
    }
  }

  /**
   * 加密令牌
   */
  private encryptToken(token: string): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(process.env.JWT_SECRET || 'default-key', 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, key);
    let encrypted = cipher.update(token, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return `${iv.toString('hex')}:${encrypted}`;
  }

  /**
   * 生成会话令牌
   */
  private generateSessionToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 提取平台信息
   */
  private extractPlatform(userAgent?: string): string {
    if (!userAgent) return 'Unknown';
    
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) return 'iOS';
    
    return 'Unknown';
  }

  /**
   * 提取浏览器信息
   */
  private extractBrowser(userAgent?: string): string {
    if (!userAgent) return 'Unknown';

    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';

    return 'Unknown';
  }

  /**
   * 解除OAuth提供商关联
   */
  async disconnectProvider(userId: string, provider: string): Promise<FederatedIdentity | null> {
    try {
      // 查找联合身份
      const federatedIdentity = await prisma.federatedIdentity.findFirst({
        where: {
          userId,
          provider,
          isActive: true
        }
      });

      if (!federatedIdentity) {
        return null;
      }

      // 检查用户是否有其他登录方式
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          federatedIdentities: {
            where: {
              isActive: true,
              id: { not: federatedIdentity.id }
            }
          }
        }
      });

      // 如果用户没有密码且这是唯一的登录方式，不允许解除关联
      if (!user?.hashedPassword && user?.federatedIdentities.length === 0) {
        throw new Error('无法解除关联：这是您唯一的登录方式，请先设置密码');
      }

      // 解除关联
      const disconnectedIdentity = await prisma.federatedIdentity.update({
        where: { id: federatedIdentity.id },
        data: { isActive: false }
      });

      // 记录审计日志
      await logAuditEvent({
        userId,
        action: 'oauth_disconnect',
        resource: 'federated_identity',
        details: {
          provider,
          federatedIdentityId: federatedIdentity.id
        }
      });

      logger.info('OAuth关联已解除', {
        userId,
        provider,
        federatedIdentityId: federatedIdentity.id
      });

      return disconnectedIdentity;

    } catch (error) {
      logger.error('解除OAuth关联失败', { error, userId, provider });
      throw error;
    }
  }
}
