/**
 * 联合身份服务
 * 处理第三方账户关联和管理
 */

import { FederatedIdentity, User } from '@prisma/client';
import { prisma } from '@/config/database';
import { logger, logAuditEvent } from '@/config/logger';

/**
 * 联合身份信息接口
 */
export interface FederatedIdentityInfo {
  id: string;
  provider: string;
  providerId: string;
  email?: string;
  name?: string;
  avatar?: string;
  lastUsedAt?: Date;
  createdAt: Date;
}

/**
 * 联合身份统计接口
 */
export interface FederatedIdentityStats {
  totalConnections: number;
  activeConnections: number;
  providerCounts: Record<string, number>;
  lastUsed?: Date;
}

/**
 * 联合身份服务类
 */
export class FederatedIdentityService {
  /**
   * 获取用户的联合身份列表
   */
  async getUserFederatedIdentities(userId: string): Promise<FederatedIdentityInfo[]> {
    try {
      const federatedIdentities = await prisma.federatedIdentity.findMany({
        where: {
          userId,
          isActive: true
        },
        select: {
          id: true,
          provider: true,
          providerId: true,
          email: true,
          name: true,
          avatar: true,
          lastUsedAt: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' }
      });

      return federatedIdentities;
    } catch (error) {
      logger.error('获取用户联合身份失败', { error, userId });
      throw new Error('获取联合身份列表失败');
    }
  }

  /**
   * 获取联合身份统计信息
   */
  async getFederatedIdentityStats(userId: string): Promise<FederatedIdentityStats> {
    try {
      const federatedIdentities = await prisma.federatedIdentity.findMany({
        where: { userId },
        select: {
          provider: true,
          isActive: true,
          lastUsedAt: true
        }
      });

      const activeConnections = federatedIdentities.filter(fi => fi.isActive);
      const providerCounts: Record<string, number> = {};
      let lastUsed: Date | undefined;

      activeConnections.forEach(fi => {
        providerCounts[fi.provider] = (providerCounts[fi.provider] || 0) + 1;
        
        if (fi.lastUsedAt && (!lastUsed || fi.lastUsedAt > lastUsed)) {
          lastUsed = fi.lastUsedAt;
        }
      });

      return {
        totalConnections: federatedIdentities.length,
        activeConnections: activeConnections.length,
        providerCounts,
        lastUsed
      };
    } catch (error) {
      logger.error('获取联合身份统计失败', { error, userId });
      throw new Error('获取联合身份统计失败');
    }
  }

  /**
   * 检查用户是否可以解除特定联合身份
   */
  async canDisconnectProvider(userId: string, connectionId: string): Promise<{
    canDisconnect: boolean;
    reason?: string;
  }> {
    try {
      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          federatedIdentities: {
            where: { isActive: true }
          }
        }
      });

      if (!user) {
        return { canDisconnect: false, reason: '用户不存在' };
      }

      // 检查是否是要解除的联合身份
      const targetConnection = user.federatedIdentities.find(fi => fi.id === connectionId);
      if (!targetConnection) {
        return { canDisconnect: false, reason: '联合身份不存在' };
      }

      // 如果用户没有密码且这是唯一的登录方式，不允许解除
      if (!user.hashedPassword && user.federatedIdentities.length === 1) {
        return { 
          canDisconnect: false, 
          reason: '这是您唯一的登录方式，请先设置密码后再解除关联' 
        };
      }

      return { canDisconnect: true };
    } catch (error) {
      logger.error('检查联合身份解除权限失败', { error, userId, connectionId });
      throw new Error('检查解除权限失败');
    }
  }

  /**
   * 解除联合身份关联
   */
  async disconnectFederatedIdentity(userId: string, connectionId: string): Promise<void> {
    try {
      // 检查是否可以解除
      const checkResult = await this.canDisconnectProvider(userId, connectionId);
      if (!checkResult.canDisconnect) {
        throw new Error(checkResult.reason || '无法解除联合身份关联');
      }

      // 获取联合身份信息
      const federatedIdentity = await prisma.federatedIdentity.findFirst({
        where: {
          id: connectionId,
          userId,
          isActive: true
        }
      });

      if (!federatedIdentity) {
        throw new Error('联合身份不存在');
      }

      // 解除关联
      await prisma.federatedIdentity.update({
        where: { id: connectionId },
        data: { isActive: false }
      });

      // 记录审计日志
      await logAuditEvent({
        userId,
        action: 'federated_identity_disconnect',
        resource: 'federated_identity',
        details: {
          connectionId,
          provider: federatedIdentity.provider,
          providerId: federatedIdentity.providerId
        }
      });

      logger.info('联合身份关联已解除', {
        userId,
        connectionId,
        provider: federatedIdentity.provider
      });
    } catch (error) {
      logger.error('解除联合身份关联失败', { error, userId, connectionId });
      throw error;
    }
  }

  /**
   * 更新联合身份最后使用时间
   */
  async updateLastUsed(userId: string, provider: string): Promise<void> {
    try {
      await prisma.federatedIdentity.updateMany({
        where: {
          userId,
          provider,
          isActive: true
        },
        data: {
          lastUsedAt: new Date()
        }
      });
    } catch (error) {
      logger.error('更新联合身份使用时间失败', { error, userId, provider });
      // 这个错误不需要抛出，因为不影响主要功能
    }
  }

  /**
   * 查找联合身份
   */
  async findFederatedIdentity(provider: string, providerId: string): Promise<FederatedIdentity | null> {
    try {
      return await prisma.federatedIdentity.findUnique({
        where: {
          provider_providerId: {
            provider,
            providerId
          }
        }
      });
    } catch (error) {
      logger.error('查找联合身份失败', { error, provider, providerId });
      return null;
    }
  }

  /**
   * 获取提供商使用统计
   */
  async getProviderStats(): Promise<Record<string, number>> {
    try {
      const stats = await prisma.federatedIdentity.groupBy({
        by: ['provider'],
        where: { isActive: true },
        _count: { provider: true }
      });

      const result: Record<string, number> = {};
      stats.forEach(stat => {
        result[stat.provider] = stat._count.provider;
      });

      return result;
    } catch (error) {
      logger.error('获取提供商统计失败', { error });
      return {};
    }
  }
}
