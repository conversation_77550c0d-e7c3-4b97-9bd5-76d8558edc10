/**
 * 认证服务
 * 处理用户注册、登录、密码管理等核心认证功能
 */

import { User, Session } from '@prisma/client';
import { prisma } from '@/config/database';
import { logger, logAuditEvent, logSecurityEvent } from '@/config/logger';
import { 
  hashPassword, 
  verifyPassword, 
  checkPasswordStrength,
  generatePasswordResetToken,
  generateEmailVerificationToken,
  generateVerificationCode
} from '@/utils/password';
import { generateTokenPair, TokenPair } from '@/utils/jwt';
import { UserProfile, DeviceInfo, LocationInfo } from '@/types/database';
import { v4 as uuidv4 } from 'uuid';

/**
 * 用户注册请求接口
 */
export interface RegisterRequest {
  email: string;
  password: string;
  nickname?: string;
  firstName?: string;
  lastName?: string;
}

/**
 * 用户登录请求接口
 */
export interface LoginRequest {
  username: string; // 可以是邮箱、手机号或用户名
  password: string;
  rememberMe?: boolean;
  deviceInfo?: DeviceInfo;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  user: UserProfile;
  tokens: TokenPair;
  sessionId: string;
  requiresMfa: boolean;
}

/**
 * 密码重置请求接口
 */
export interface PasswordResetRequest {
  email: string;
  ipAddress?: string;
}

/**
 * 密码重置确认接口
 */
export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
  ipAddress?: string;
}

/**
 * 认证服务类
 */
export class AuthService {
  /**
   * 用户注册
   * @param registerData 注册数据
   * @param ipAddress IP地址
   * @returns 注册结果
   */
  async register(registerData: RegisterRequest, ipAddress?: string): Promise<{ userId: string; status: string }> {
    const { email, password, nickname, firstName, lastName } = registerData;

    try {
      // 检查邮箱是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        logSecurityEvent('registration_attempt_duplicate_email', { email }, undefined, ipAddress);
        throw new Error('邮箱已被注册');
      }

      // 检查密码强度
      const passwordStrength = checkPasswordStrength(password);
      if (!passwordStrength.isValid) {
        throw new Error(`密码强度不足: ${passwordStrength.errors.join(', ')}`);
      }

      // 加密密码
      const passwordHash = await hashPassword(password);

      // 生成邮箱验证令牌
      const verificationToken = generateEmailVerificationToken();

      // 创建用户
      const user = await prisma.user.create({
        data: {
          email,
          passwordHash,
          nickname,
          firstName,
          lastName,
          emailVerified: false,
          isActive: true
        }
      });

      // 分配默认用户角色
      const userRole = await prisma.role.findUnique({
        where: { name: 'user' }
      });

      if (userRole) {
        await prisma.userRole.create({
          data: {
            userId: user.id,
            roleId: userRole.id
          }
        });
      }

      // 记录审计日志
      logAuditEvent('user_register', 'user', user.id, {
        email,
        nickname,
        ipAddress
      });

      logger.info('用户注册成功', { userId: user.id, email });

      // TODO: 发送邮箱验证邮件
      // await this.sendVerificationEmail(user.email, verificationToken);

      return {
        userId: user.id,
        status: 'pending_verification'
      };

    } catch (error) {
      logger.error('用户注册失败', { error, email, ipAddress });
      throw error;
    }
  }

  /**
   * 用户登录
   * @param loginData 登录数据
   * @returns 登录结果
   */
  async login(loginData: LoginRequest): Promise<LoginResponse> {
    const { username, password, rememberMe, deviceInfo, ipAddress, userAgent } = loginData;

    try {
      // 查找用户（支持邮箱、手机号、用户名）
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: username },
            { phone: username },
            { username: username }
          ],
          isActive: true
        },
        include: {
          mfaDevices: {
            where: { isActive: true }
          }
        }
      });

      if (!user) {
        logSecurityEvent('login_attempt_user_not_found', { username }, undefined, ipAddress);
        throw new Error('用户名或密码错误');
      }

      // 检查账户是否被锁定
      if (user.isLocked) {
        logSecurityEvent('login_attempt_account_locked', { username }, user.id, ipAddress);
        throw new Error('账户已被锁定，请联系管理员');
      }

      // 验证密码
      if (!user.passwordHash || !(await verifyPassword(password, user.passwordHash))) {
        logSecurityEvent('login_attempt_invalid_password', { username }, user.id, ipAddress);
        throw new Error('用户名或密码错误');
      }

      // 检查是否需要MFA
      const requiresMfa = user.mfaDevices.length > 0;

      // 创建会话
      const sessionToken = uuidv4();
      const expiresAt = new Date();
      
      if (rememberMe) {
        expiresAt.setDate(expiresAt.getDate() + 30); // 30天
      } else {
        expiresAt.setMinutes(expiresAt.getMinutes() + 30); // 30分钟
      }

      const session = await prisma.session.create({
        data: {
          userId: user.id,
          sessionToken,
          deviceInfo: deviceInfo ? JSON.stringify(deviceInfo) : null,
          ipAddress: ipAddress || '',
          userAgent,
          expiresAt,
          authMethod: 'password',
          mfaVerified: !requiresMfa // 如果不需要MFA，直接标记为已验证
        }
      });

      // 更新用户最后登录信息
      await prisma.user.update({
        where: { id: user.id },
        data: {
          lastLoginAt: new Date(),
          lastLoginIp: ipAddress
        }
      });

      // 生成JWT令牌
      const tokens = generateTokenPair(user, session.id);

      // 创建用户资料对象（排除敏感信息）
      const userProfile: UserProfile = {
        id: user.id,
        email: user.email,
        phone: user.phone,
        username: user.username,
        nickname: user.nickname,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        emailVerified: user.emailVerified,
        phoneVerified: user.phoneVerified,
        isActive: user.isActive,
        isLocked: user.isLocked,
        lockReason: user.lockReason,
        lastLoginAt: user.lastLoginAt,
        lastLoginIp: user.lastLoginIp,
        passwordChangedAt: user.passwordChangedAt
      };

      // 记录审计日志
      logAuditEvent('user_login', 'session', user.id, {
        sessionId: session.id,
        ipAddress,
        userAgent,
        requiresMfa
      });

      logger.info('用户登录成功', { 
        userId: user.id, 
        sessionId: session.id, 
        requiresMfa,
        ipAddress 
      });

      return {
        user: userProfile,
        tokens,
        sessionId: session.id,
        requiresMfa
      };

    } catch (error) {
      logger.error('用户登录失败', { error, username, ipAddress });
      throw error;
    }
  }

  /**
   * 用户登出
   * @param sessionToken 会话令牌
   * @param ipAddress IP地址
   */
  async logout(sessionToken: string, ipAddress?: string): Promise<void> {
    try {
      const session = await prisma.session.findUnique({
        where: { sessionToken },
        include: { user: true }
      });

      if (!session) {
        throw new Error('无效的会话');
      }

      // 标记会话为非活跃
      await prisma.session.update({
        where: { id: session.id },
        data: { isActive: false }
      });

      // 记录审计日志
      logAuditEvent('user_logout', 'session', session.userId, {
        sessionId: session.id,
        ipAddress
      });

      logger.info('用户登出成功', {
        userId: session.userId,
        sessionId: session.id,
        ipAddress
      });

    } catch (error) {
      logger.error('用户登出失败', { error, sessionToken, ipAddress });
      throw error;
    }
  }

  /**
   * 刷新令牌
   * @param refreshToken 刷新令牌
   * @returns 新的令牌对
   */
  async refreshToken(refreshToken: string): Promise<TokenPair> {
    try {
      // 验证刷新令牌
      const payload = verifyRefreshToken(refreshToken);

      // 查找用户和会话
      const user = await prisma.user.findUnique({
        where: { id: payload.userId, isActive: true }
      });

      if (!user) {
        throw new Error('用户不存在或已被禁用');
      }

      // 检查刷新令牌是否在数据库中存在且有效
      const storedToken = await prisma.refreshToken.findFirst({
        where: {
          token: refreshToken,
          userId: user.id,
          isActive: true,
          expiresAt: { gt: new Date() }
        }
      });

      if (!storedToken) {
        throw new Error('无效的刷新令牌');
      }

      // 生成新的令牌对
      const tokens = generateTokenPair(user, payload.sessionId);

      // 更新刷新令牌
      await prisma.refreshToken.update({
        where: { id: storedToken.id },
        data: { token: tokens.refreshToken }
      });

      logger.info('令牌刷新成功', { userId: user.id });

      return tokens;

    } catch (error) {
      logger.error('令牌刷新失败', { error });
      throw error;
    }
  }

  /**
   * 发起密码重置
   * @param resetData 重置请求数据
   */
  async requestPasswordReset(resetData: PasswordResetRequest): Promise<void> {
    const { email, ipAddress } = resetData;

    try {
      const user = await prisma.user.findUnique({
        where: { email, isActive: true }
      });

      if (!user) {
        // 为了安全，即使用户不存在也返回成功
        logger.warn('密码重置请求 - 用户不存在', { email, ipAddress });
        return;
      }

      // 生成重置令牌
      const resetToken = generatePasswordResetToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1); // 1小时有效期

      // 存储重置令牌（这里简化处理，实际应该有专门的表）
      // TODO: 创建password_reset_tokens表

      // 记录审计日志
      logAuditEvent('password_reset_request', 'user', user.id, {
        email,
        ipAddress
      });

      // TODO: 发送密码重置邮件
      // await this.sendPasswordResetEmail(user.email, resetToken);

      logger.info('密码重置请求成功', { userId: user.id, email });

    } catch (error) {
      logger.error('密码重置请求失败', { error, email, ipAddress });
      throw error;
    }
  }

  /**
   * 确认密码重置
   * @param resetData 重置确认数据
   */
  async confirmPasswordReset(resetData: PasswordResetConfirm): Promise<void> {
    const { token, newPassword, ipAddress } = resetData;

    try {
      // TODO: 验证重置令牌
      // 这里需要从password_reset_tokens表中查找并验证令牌

      // 检查新密码强度
      const passwordStrength = checkPasswordStrength(newPassword);
      if (!passwordStrength.isValid) {
        throw new Error(`密码强度不足: ${passwordStrength.errors.join(', ')}`);
      }

      // 加密新密码
      const passwordHash = await hashPassword(newPassword);

      // TODO: 更新用户密码
      // const user = await prisma.user.update({
      //   where: { id: userId },
      //   data: {
      //     passwordHash,
      //     passwordChangedAt: new Date()
      //   }
      // });

      // 记录审计日志
      // logAuditEvent('password_reset_confirm', 'user', user.id, {
      //   ipAddress
      // });

      logger.info('密码重置确认成功');

    } catch (error) {
      logger.error('密码重置确认失败', { error, ipAddress });
      throw error;
    }
  }

  /**
   * 修改密码
   * @param userId 用户ID
   * @param currentPassword 当前密码
   * @param newPassword 新密码
   * @param ipAddress IP地址
   */
  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    ipAddress?: string
  ): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId, isActive: true }
      });

      if (!user || !user.passwordHash) {
        throw new Error('用户不存在或未设置密码');
      }

      // 验证当前密码
      if (!(await verifyPassword(currentPassword, user.passwordHash))) {
        logSecurityEvent('password_change_invalid_current', {}, userId, ipAddress);
        throw new Error('当前密码错误');
      }

      // 检查新密码强度
      const passwordStrength = checkPasswordStrength(newPassword);
      if (!passwordStrength.isValid) {
        throw new Error(`密码强度不足: ${passwordStrength.errors.join(', ')}`);
      }

      // 检查新密码是否与当前密码相同
      if (await verifyPassword(newPassword, user.passwordHash)) {
        throw new Error('新密码不能与当前密码相同');
      }

      // 加密新密码
      const passwordHash = await hashPassword(newPassword);

      // 更新密码
      await prisma.user.update({
        where: { id: userId },
        data: {
          passwordHash,
          passwordChangedAt: new Date()
        }
      });

      // 记录审计日志
      logAuditEvent('password_change', 'user', userId, {
        ipAddress
      });

      logger.info('密码修改成功', { userId });

    } catch (error) {
      logger.error('密码修改失败', { error, userId, ipAddress });
      throw error;
    }
  }

  /**
   * 验证邮箱
   * @param token 验证令牌
   * @param ipAddress IP地址
   */
  async verifyEmail(token: string, ipAddress?: string): Promise<void> {
    try {
      // TODO: 验证邮箱验证令牌
      // 这里需要从email_verification_tokens表中查找并验证令牌

      // TODO: 更新用户邮箱验证状态
      // const user = await prisma.user.update({
      //   where: { id: userId },
      //   data: { emailVerified: true }
      // });

      // 记录审计日志
      // logAuditEvent('email_verify', 'user', user.id, {
      //   ipAddress
      // });

      logger.info('邮箱验证成功');

    } catch (error) {
      logger.error('邮箱验证失败', { error, ipAddress });
      throw error;
    }
  }

  /**
   * 获取用户会话列表
   * @param userId 用户ID
   * @returns 会话列表
   */
  async getUserSessions(userId: string): Promise<Session[]> {
    try {
      const sessions = await prisma.session.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() }
        },
        orderBy: { lastAccessedAt: 'desc' }
      });

      return sessions;

    } catch (error) {
      logger.error('获取用户会话失败', { error, userId });
      throw error;
    }
  }

  /**
   * 终止指定会话
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @param ipAddress IP地址
   */
  async terminateSession(userId: string, sessionId: string, ipAddress?: string): Promise<void> {
    try {
      const session = await prisma.session.findFirst({
        where: {
          id: sessionId,
          userId,
          isActive: true
        }
      });

      if (!session) {
        throw new Error('会话不存在');
      }

      await prisma.session.update({
        where: { id: sessionId },
        data: { isActive: false }
      });

      // 记录审计日志
      logAuditEvent('session_terminate', 'session', userId, {
        sessionId,
        ipAddress
      });

      logger.info('会话终止成功', { userId, sessionId });

    } catch (error) {
      logger.error('会话终止失败', { error, userId, sessionId });
      throw error;
    }
  }

  /**
   * 终止用户所有会话（除当前会话外）
   * @param userId 用户ID
   * @param currentSessionId 当前会话ID
   * @param ipAddress IP地址
   */
  async terminateAllOtherSessions(userId: string, currentSessionId: string, ipAddress?: string): Promise<void> {
    try {
      await prisma.session.updateMany({
        where: {
          userId,
          id: { not: currentSessionId },
          isActive: true
        },
        data: { isActive: false }
      });

      // 记录审计日志
      logAuditEvent('sessions_terminate_all_others', 'session', userId, {
        currentSessionId,
        ipAddress
      });

      logger.info('其他会话终止成功', { userId, currentSessionId });

    } catch (error) {
      logger.error('其他会话终止失败', { error, userId, currentSessionId });
      throw error;
    }
  }
}
