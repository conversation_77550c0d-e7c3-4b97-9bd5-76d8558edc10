/**
 * 非标准应用控制器
 * 处理非标准应用的认证和管理请求
 */

import { Request, Response } from 'express';
import { ProtocolAdapterService } from '@/services/protocol-adapter.service';
import { PluginManager } from '@/services/plugin-manager.service';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { AuthenticatedRequest } from '@/types/auth';
import { 
  AuthenticationRequest, 
  NonStandardAppType,
  ProtocolAdapterError 
} from '@/types/protocol-adapter';

/**
 * 非标准应用控制器
 */
export class NonStandardAppController {
  private protocolAdapterService: ProtocolAdapterService;
  private pluginManager: PluginManager;

  constructor(
    protocolAdapterService: ProtocolAdapterService,
    pluginManager: PluginManager
  ) {
    this.protocolAdapterService = protocolAdapterService;
    this.pluginManager = pluginManager;
  }

  /**
   * 处理自定义认证请求
   */
  handleCustomAuth = async (req: Request, res: Response): Promise<void> => {
    try {
      const { applicationId, protocolName } = req.params;
      const { 
        client_id, 
        redirect_uri, 
        scope, 
        state, 
        custom_params 
      } = req.query;

      // 构建认证请求
      const authRequest: AuthenticationRequest = {
        applicationId,
        clientId: client_id as string,
        redirectUri: redirect_uri as string,
        scope: scope ? (scope as string).split(' ') : undefined,
        state: state as string,
        customParams: custom_params ? JSON.parse(custom_params as string) : undefined,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      };

      // 如果用户已认证，添加用户ID
      if (req.user) {
        authRequest.userId = (req.user as any).userId;
      }

      // 处理认证请求
      const response = await this.protocolAdapterService.handleAuthRequest(
        applicationId,
        protocolName,
        authRequest
      );

      if (response.success) {
        if (response.redirectUrl) {
          // 重定向到指定URL
          res.redirect(response.redirectUrl);
        } else {
          // 返回认证结果
          res.json({
            success: true,
            tokens: response.tokens,
            user: response.user,
            customData: response.customData
          });
        }
      } else {
        res.status(400).json({
          error: response.error?.code || 'authentication_failed',
          message: response.error?.message || '认证失败'
        });
      }

    } catch (error) {
      logger.error('处理自定义认证请求失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '服务器内部错误'
      });
    }
  };

  /**
   * 处理令牌端点请求
   */
  handleTokenEndpoint = async (req: Request, res: Response): Promise<void> => {
    try {
      const { applicationId, protocolName } = req.params;

      // 获取应用配置
      const appConfig = this.protocolAdapterService.getApplicationConfig(
        applicationId,
        protocolName
      );

      if (!appConfig) {
        res.status(404).json({
          error: 'invalid_client',
          message: '应用配置未找到'
        });
        return;
      }

      // 获取协议适配器
      const adapter = this.protocolAdapterService['activeAdapters'].get(protocolName);
      if (!adapter) {
        res.status(400).json({
          error: 'unsupported_protocol',
          message: `不支持的协议: ${protocolName}`
        });
        return;
      }

      // 处理令牌请求
      const response = await adapter.handleTokenRequest(req);

      if (response.success) {
        res.json(response.tokens);
      } else {
        res.status(400).json({
          error: response.error?.code || 'token_error',
          error_description: response.error?.message || '令牌处理失败'
        });
      }

    } catch (error) {
      logger.error('处理令牌端点请求失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '服务器内部错误'
      });
    }
  };

  /**
   * 处理用户信息端点请求
   */
  handleUserInfoEndpoint = async (req: Request, res: Response): Promise<void> => {
    try {
      const { applicationId, protocolName } = req.params;

      // 获取协议适配器
      const adapter = this.protocolAdapterService['activeAdapters'].get(protocolName);
      if (!adapter) {
        res.status(400).json({
          error: 'unsupported_protocol',
          message: `不支持的协议: ${protocolName}`
        });
        return;
      }

      // 处理用户信息请求
      const response = await adapter.handleUserInfoRequest(req);

      if (response.success) {
        res.json(response.user);
      } else {
        res.status(401).json({
          error: response.error?.code || 'invalid_token',
          error_description: response.error?.message || '无效的访问令牌'
        });
      }

    } catch (error) {
      logger.error('处理用户信息端点请求失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '服务器内部错误'
      });
    }
  };

  /**
   * 获取协议元数据
   */
  getProtocolMetadata = async (req: Request, res: Response): Promise<void> => {
    try {
      const { protocolName } = req.params;

      // 获取协议适配器
      const adapter = this.protocolAdapterService['activeAdapters'].get(protocolName);
      if (!adapter) {
        res.status(404).json({
          error: 'protocol_not_found',
          message: `协议 ${protocolName} 未找到`
        });
        return;
      }

      // 生成元数据
      const metadata = await adapter.generateMetadata();
      res.json(metadata);

    } catch (error) {
      logger.error('获取协议元数据失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '服务器内部错误'
      });
    }
  };

  /**
   * 获取支持的协议列表
   */
  getSupportedProtocols = async (req: Request, res: Response): Promise<void> => {
    try {
      const protocols = this.protocolAdapterService.getSupportedProtocols();
      const pluginProtocols = this.pluginManager.getAvailableProtocolAdapters();

      res.json({
        builtin_protocols: protocols,
        plugin_protocols: pluginProtocols,
        all_protocols: [...new Set([...protocols, ...pluginProtocols])]
      });

    } catch (error) {
      logger.error('获取支持的协议列表失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '服务器内部错误'
      });
    }
  };

  /**
   * 创建非标准应用配置
   */
  createNonStandardApp = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const {
        name,
        description,
        appType,
        protocolName,
        protocolConfig,
        customAuthFlow,
        webhookUrls,
        apiKeyEnabled,
        customSettings
      } = req.body;

      // 生成客户端凭据
      const clientId = this.generateClientId();
      const clientSecret = this.generateClientSecret();

      // 创建应用
      const application = await prisma.application.create({
        data: {
          name,
          description,
          clientId,
          clientSecret,
          appType,
          supportedProtocols: [protocolName],
          customProtocolConfig: protocolConfig,
          customAuthFlow,
          webhookUrls,
          apiKeyEnabled,
          customSettings,
          redirectUris: req.body.redirectUris || [],
          allowedOrigins: req.body.allowedOrigins || []
        }
      });

      // 创建协议配置
      if (protocolConfig) {
        await prisma.applicationProtocolConfig.create({
          data: {
            applicationId: application.id,
            protocolName,
            config: protocolConfig,
            customHandlers: req.body.customHandlers,
            webhooks: webhookUrls
          }
        });
      }

      res.status(201).json({
        id: application.id,
        name: application.name,
        clientId: application.clientId,
        appType: application.appType,
        supportedProtocols: application.supportedProtocols,
        createdAt: application.createdAt
      });

    } catch (error) {
      logger.error('创建非标准应用失败', { error });
      res.status(500).json({
        error: 'creation_failed',
        message: '创建应用失败'
      });
    }
  };

  /**
   * 更新非标准应用配置
   */
  updateNonStandardApp = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { applicationId } = req.params;
      const updateData = req.body;

      // 更新应用
      const application = await prisma.application.update({
        where: { id: applicationId },
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      });

      // 重新加载协议适配器配置
      await this.protocolAdapterService.reloadConfigs();

      res.json({
        id: application.id,
        name: application.name,
        appType: application.appType,
        supportedProtocols: application.supportedProtocols,
        updatedAt: application.updatedAt
      });

    } catch (error) {
      logger.error('更新非标准应用失败', { error });
      res.status(500).json({
        error: 'update_failed',
        message: '更新应用失败'
      });
    }
  };

  /**
   * 测试非标准应用连接
   */
  testAppConnection = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { applicationId } = req.params;
      const { protocolName } = req.body;

      // 获取应用配置
      const application = await prisma.application.findUnique({
        where: { id: applicationId }
      });

      if (!application) {
        res.status(404).json({
          error: 'app_not_found',
          message: '应用未找到'
        });
        return;
      }

      // 执行连接测试
      const testResult = await this.performConnectionTest(application, protocolName);

      res.json({
        success: testResult.success,
        message: testResult.message,
        details: testResult.details
      });

    } catch (error) {
      logger.error('测试应用连接失败', { error });
      res.status(500).json({
        error: 'test_failed',
        message: '连接测试失败'
      });
    }
  };

  /**
   * 执行自定义处理器
   */
  executeCustomHandler = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { handlerName } = req.params;
      const { args } = req.body;

      // 执行自定义处理器
      const result = await this.pluginManager.executeCustomHandler(handlerName, ...args);

      res.json({
        success: true,
        result
      });

    } catch (error) {
      logger.error('执行自定义处理器失败', { error });
      res.status(500).json({
        error: 'handler_execution_failed',
        message: error.message
      });
    }
  };

  // 私有方法

  private generateClientId(): string {
    return `nsa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateClientSecret(): string {
    return require('crypto').randomBytes(32).toString('hex');
  }

  private async performConnectionTest(application: any, protocolName: string): Promise<any> {
    try {
      // 根据协议类型执行不同的测试
      switch (protocolName) {
        case 'custom-oauth':
          return await this.testCustomOAuthConnection(application);
        case 'legacy-system':
          return await this.testLegacySystemConnection(application);
        case 'webhook':
          return await this.testWebhookConnection(application);
        default:
          return {
            success: false,
            message: `不支持的协议测试: ${protocolName}`
          };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message,
        details: { error: error.stack }
      };
    }
  }

  private async testCustomOAuthConnection(application: any): Promise<any> {
    // 测试自定义OAuth连接
    return {
      success: true,
      message: '自定义OAuth连接测试成功',
      details: {
        clientId: application.clientId,
        endpoints: application.customProtocolConfig?.endpoints
      }
    };
  }

  private async testLegacySystemConnection(application: any): Promise<any> {
    // 测试遗留系统连接
    const config = application.customProtocolConfig;
    if (!config?.legacy_api_url) {
      return {
        success: false,
        message: '缺少遗留系统API URL配置'
      };
    }

    // 这里可以实际测试连接
    return {
      success: true,
      message: '遗留系统连接测试成功',
      details: {
        apiUrl: config.legacy_api_url
      }
    };
  }

  private async testWebhookConnection(application: any): Promise<any> {
    // 测试Webhook连接
    const webhookUrls = application.webhookUrls;
    if (!webhookUrls) {
      return {
        success: false,
        message: '未配置Webhook URL'
      };
    }

    return {
      success: true,
      message: 'Webhook连接测试成功',
      details: {
        webhooks: webhookUrls
      }
    };
  }
}
