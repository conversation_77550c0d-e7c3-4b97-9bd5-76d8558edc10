/**
 * 认证控制器
 * 处理认证相关的HTTP请求
 */

import { Request, Response } from 'express';
import { AuthService } from '@/services/auth.service';
import { logger } from '@/config/logger';
import { validateRegisterRequest, validateLoginRequest } from '@/utils/validation';
import { verifyAccessToken } from '@/utils/jwt';
import { prisma } from '@/config/database';

/**
 * 扩展Request接口，添加用户信息
 */
export interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    email: string;
    sessionId?: string;
  };
}

/**
 * 认证控制器类
 */
export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  /**
   * 用户注册
   */
  register = async (req: Request, res: Response): Promise<void> => {
    try {
      // 验证请求数据
      const validationResult = validateRegisterRequest(req.body);
      if (!validationResult.isValid) {
        res.status(400).json({
          error: 'validation_error',
          message: '请求数据验证失败',
          details: validationResult.errors
        });
        return;
      }

      const { email, password, nickname, firstName, lastName } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;

      // 调用认证服务
      const result = await this.authService.register({
        email,
        password,
        nickname,
        firstName,
        lastName
      }, ipAddress);

      res.status(201).json({
        userId: result.userId,
        status: result.status,
        message: '注册成功，请检查邮箱进行验证'
      });

    } catch (error) {
      logger.error('注册请求处理失败', { error, body: req.body });
      
      res.status(400).json({
        error: 'registration_failed',
        message: (error as Error).message || '注册失败'
      });
    }
  };

  /**
   * 用户登录
   */
  login = async (req: Request, res: Response): Promise<void> => {
    try {
      // 验证请求数据
      const validationResult = validateLoginRequest(req.body);
      if (!validationResult.isValid) {
        res.status(400).json({
          error: 'validation_error',
          message: '请求数据验证失败',
          details: validationResult.errors
        });
        return;
      }

      const { username, password, rememberMe } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      // 调用认证服务
      const result = await this.authService.login({
        username,
        password,
        rememberMe,
        ipAddress,
        userAgent
      });

      res.status(200).json({
        user: result.user,
        accessToken: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn,
        requiresMfa: result.requiresMfa,
        sessionId: result.sessionId
      });

    } catch (error) {
      logger.error('登录请求处理失败', { error, username: req.body.username });
      
      res.status(401).json({
        error: 'authentication_failed',
        message: (error as Error).message || '认证失败'
      });
    }
  };

  /**
   * 用户登出
   */
  logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { refreshToken } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;

      if (!refreshToken) {
        res.status(400).json({
          error: 'missing_refresh_token',
          message: '缺少刷新令牌'
        });
        return;
      }

      // 从刷新令牌中获取会话信息
      // TODO: 实现从refreshToken获取sessionToken的逻辑
      const sessionToken = 'session-token-from-refresh-token';

      await this.authService.logout(sessionToken, ipAddress);

      res.status(204).send();

    } catch (error) {
      logger.error('登出请求处理失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'logout_failed',
        message: (error as Error).message || '登出失败'
      });
    }
  };

  /**
   * 刷新令牌
   */
  refreshToken = async (req: Request, res: Response): Promise<void> => {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        res.status(400).json({
          error: 'missing_refresh_token',
          message: '缺少刷新令牌'
        });
        return;
      }

      const tokens = await this.authService.refreshToken(refreshToken);

      res.status(200).json({
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn
      });

    } catch (error) {
      logger.error('令牌刷新请求处理失败', { error });
      
      res.status(401).json({
        error: 'token_refresh_failed',
        message: (error as Error).message || '令牌刷新失败'
      });
    }
  };

  /**
   * 忘记密码
   */
  forgotPassword = async (req: Request, res: Response): Promise<void> => {
    try {
      const { email } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;

      if (!email) {
        res.status(400).json({
          error: 'missing_email',
          message: '缺少邮箱地址'
        });
        return;
      }

      await this.authService.requestPasswordReset({
        email,
        ipAddress
      });

      // 为了安全，总是返回成功响应
      res.status(202).json({
        message: '如果邮箱存在，重置链接已发送'
      });

    } catch (error) {
      logger.error('忘记密码请求处理失败', { error, email: req.body.email });
      
      // 为了安全，即使出错也返回成功响应
      res.status(202).json({
        message: '如果邮箱存在，重置链接已发送'
      });
    }
  };

  /**
   * 重置密码
   */
  resetPassword = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token, newPassword } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;

      if (!token || !newPassword) {
        res.status(400).json({
          error: 'missing_parameters',
          message: '缺少必需参数'
        });
        return;
      }

      await this.authService.confirmPasswordReset({
        token,
        newPassword,
        ipAddress
      });

      res.status(204).send();

    } catch (error) {
      logger.error('重置密码请求处理失败', { error });
      
      res.status(400).json({
        error: 'password_reset_failed',
        message: (error as Error).message || '密码重置失败'
      });
    }
  };

  /**
   * 修改密码
   */
  changePassword = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const { currentPassword, newPassword } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userId = req.user?.userId;

      if (!currentPassword || !newPassword) {
        res.status(400).json({
          error: 'missing_parameters',
          message: '缺少必需参数'
        });
        return;
      }

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      await this.authService.changePassword(
        userId,
        currentPassword,
        newPassword,
        ipAddress
      );

      res.status(204).send();

    } catch (error) {
      logger.error('修改密码请求处理失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'password_change_failed',
        message: (error as Error).message || '密码修改失败'
      });
    }
  };

  /**
   * 验证邮箱
   */
  verifyEmail = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token } = req.query;
      const ipAddress = req.ip || req.connection.remoteAddress;

      if (!token || typeof token !== 'string') {
        res.status(400).json({
          error: 'missing_token',
          message: '缺少验证令牌'
        });
        return;
      }

      await this.authService.verifyEmail(token, ipAddress);

      res.status(200).json({
        message: '邮箱验证成功'
      });

    } catch (error) {
      logger.error('邮箱验证请求处理失败', { error });

      res.status(400).json({
        error: 'email_verification_failed',
        message: (error as Error).message || '邮箱验证失败'
      });
    }
  };

  /**
   * 验证访问令牌（供API网关使用）
   */
  validateToken = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token } = req.body;

      if (!token) {
        res.status(400).json({
          error: 'missing_token',
          message: '缺少令牌参数'
        });
        return;
      }

      // 验证JWT令牌
      const payload = verifyAccessToken(token);

      // 检查用户是否存在且活跃
      const user = await prisma.user.findUnique({
        where: {
          id: payload.userId,
          isActive: true,
          isLocked: false
        },
        select: {
          id: true,
          email: true,
          nickname: true,
          isActive: true,
          isLocked: true,
          userRoles: {
            include: {
              role: true
            }
          }
        }
      });

      if (!user) {
        res.status(401).json({
          error: 'invalid_token',
          message: '令牌无效或用户不存在'
        });
        return;
      }

      // 返回用户信息和权限
      const roles = user.userRoles.map(ur => ur.role.name);
      const permissions: string[] = [];
      user.userRoles.forEach(ur => {
        permissions.push(...ur.role.permissions);
      });

      res.status(200).json({
        valid: true,
        user: {
          id: user.id,
          email: user.email,
          nickname: user.nickname,
          roles,
          permissions: [...new Set(permissions)] // 去重
        },
        exp: payload.exp,
        iat: payload.iat
      });

    } catch (error) {
      logger.error('令牌验证失败', { error });

      res.status(401).json({
        error: 'invalid_token',
        message: '令牌验证失败'
      });
    }
  };

  /**
   * 令牌内省端点（RFC 7662）
   */
  introspectToken = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token, token_type_hint } = req.body;

      if (!token) {
        res.status(400).json({
          error: 'invalid_request',
          message: '缺少token参数'
        });
        return;
      }

      try {
        // 验证JWT令牌
        const payload = verifyAccessToken(token);

        // 检查用户是否存在且活跃
        const user = await prisma.user.findUnique({
          where: {
            id: payload.userId,
            isActive: true,
            isLocked: false
          },
          select: {
            id: true,
            email: true,
            nickname: true,
            userRoles: {
              include: {
                role: true
              }
            }
          }
        });

        if (!user) {
          res.status(200).json({
            active: false
          });
          return;
        }

        // 收集权限和角色
        const roles = user.userRoles.map(ur => ur.role.name);
        const permissions: string[] = [];
        user.userRoles.forEach(ur => {
          permissions.push(...ur.role.permissions);
        });

        // 返回RFC 7662标准格式
        res.status(200).json({
          active: true,
          client_id: payload.clientId || 'default',
          username: user.email,
          scope: [...new Set(permissions)].join(' '),
          exp: payload.exp,
          iat: payload.iat,
          sub: user.id,
          aud: payload.aud || 'api-gateway',
          iss: payload.iss || 'id-provider',
          token_type: 'Bearer',
          roles: roles
        });

      } catch (tokenError) {
        // 令牌无效或过期
        res.status(200).json({
          active: false
        });
      }

    } catch (error) {
      logger.error('令牌内省失败', { error });

      res.status(500).json({
        error: 'server_error',
        message: '服务器内部错误'
      });
    }
  };
}
