/**
 * OAuth控制器
 * 处理第三方OAuth登录的HTTP请求
 */

import { Request, Response, NextFunction } from 'express';
import passport from 'passport';
import { OAuthService } from '@/services/oauth.service';
import { logger } from '@/config/logger';
import { config } from '@/config';
import { OAuthProfile } from '@/config/passport';

/**
 * OAuth控制器类
 */
export class OAuthController {
  private oauthService: OAuthService;

  constructor() {
    this.oauthService = new OAuthService();
  }

  /**
   * 发起Google OAuth登录
   */
  googleAuth = passport.authenticate('google', {
    scope: ['profile', 'email']
  });

  /**
   * Google OAuth回调处理
   */
  googleCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    passport.authenticate('google', { session: false }, async (err, profile: OAuthProfile) => {
      try {
        if (err) {
          logger.error('Google OAuth认证失败', { error: err });
          return this.redirectWithError(res, 'oauth_error', 'Google认证失败');
        }

        if (!profile) {
          logger.warn('Google OAuth认证被拒绝');
          return this.redirectWithError(res, 'oauth_denied', 'Google认证被拒绝');
        }

        const ipAddress = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent');

        const result = await this.oauthService.handleOAuthLogin(profile, ipAddress, userAgent);

        // 重定向到前端，携带令牌信息
        const redirectUrl = this.buildSuccessRedirectUrl(result);
        res.redirect(redirectUrl);

      } catch (error) {
        logger.error('Google OAuth回调处理失败', { error });
        this.redirectWithError(res, 'oauth_callback_error', 'Google登录处理失败');
      }
    })(req, res, next);
  };

  /**
   * 发起GitHub OAuth登录
   */
  githubAuth = passport.authenticate('github', {
    scope: ['user:email']
  });

  /**
   * GitHub OAuth回调处理
   */
  githubCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    passport.authenticate('github', { session: false }, async (err, profile: OAuthProfile) => {
      try {
        if (err) {
          logger.error('GitHub OAuth认证失败', { error: err });
          return this.redirectWithError(res, 'oauth_error', 'GitHub认证失败');
        }

        if (!profile) {
          logger.warn('GitHub OAuth认证被拒绝');
          return this.redirectWithError(res, 'oauth_denied', 'GitHub认证被拒绝');
        }

        const ipAddress = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent');

        const result = await this.oauthService.handleOAuthLogin(profile, ipAddress, userAgent);

        // 重定向到前端，携带令牌信息
        const redirectUrl = this.buildSuccessRedirectUrl(result);
        res.redirect(redirectUrl);

      } catch (error) {
        logger.error('GitHub OAuth回调处理失败', { error });
        this.redirectWithError(res, 'oauth_callback_error', 'GitHub登录处理失败');
      }
    })(req, res, next);
  };

  /**
   * 获取支持的OAuth提供商列表
   */
  getProviders = async (req: Request, res: Response): Promise<void> => {
    try {
      const providers = [];

      // 检查Google配置
      if (config.oauth.google.clientId && config.oauth.google.clientSecret) {
        providers.push({
          name: 'google',
          displayName: 'Google',
          authUrl: '/api/v1/auth/google',
          enabled: true
        });
      }

      // 检查GitHub配置
      if (config.oauth.github.clientId && config.oauth.github.clientSecret) {
        providers.push({
          name: 'github',
          displayName: 'GitHub',
          authUrl: '/api/v1/auth/github',
          enabled: true
        });
      }

      // 检查微信配置
      if (config.oauth.wechat.appId && config.oauth.wechat.appSecret) {
        providers.push({
          name: 'wechat',
          displayName: '微信',
          authUrl: '/api/v1/auth/wechat',
          enabled: true
        });
      }

      // 检查微博配置
      if (config.oauth.weibo.clientId && config.oauth.weibo.clientSecret) {
        providers.push({
          name: 'weibo',
          displayName: '微博',
          authUrl: '/api/v1/auth/weibo',
          enabled: true
        });
      }

      res.status(200).json({
        providers,
        total: providers.length
      });

    } catch (error) {
      logger.error('获取OAuth提供商列表失败', { error });
      res.status(500).json({
        error: 'get_providers_failed',
        message: '获取OAuth提供商列表失败'
      });
    }
  };

  /**
   * 构建成功重定向URL
   */
  private buildSuccessRedirectUrl(result: any): string {
    const baseUrl = config.server.frontendUrl;
    const params = new URLSearchParams({
      access_token: result.tokens.accessToken,
      refresh_token: result.tokens.refreshToken,
      expires_in: result.tokens.expiresIn.toString(),
      user_id: result.user.id,
      is_new_user: result.isNewUser.toString(),
      provider: result.federatedIdentity.provider
    });

    return `${baseUrl}/auth/callback?${params.toString()}`;
  }

  /**
   * 错误重定向
   */
  private redirectWithError(res: Response, error: string, message: string): void {
    const baseUrl = config.server.frontendUrl;
    const params = new URLSearchParams({
      error,
      message
    });

    res.redirect(`${baseUrl}/auth/error?${params.toString()}`);
  }

  /**
   * 发起微信OAuth登录
   */
  wechatAuth = passport.authenticate('wechat');

  /**
   * 微信OAuth回调处理
   */
  wechatCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    passport.authenticate('wechat', { session: false }, async (err, profile: OAuthProfile) => {
      try {
        if (err) {
          logger.error('微信OAuth认证失败', { error: err });
          return this.redirectWithError(res, 'oauth_error', '微信认证失败');
        }

        if (!profile) {
          logger.warn('微信OAuth认证被拒绝');
          return this.redirectWithError(res, 'oauth_denied', '微信认证被拒绝');
        }

        const ipAddress = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent');

        const result = await this.oauthService.handleOAuthLogin(profile, ipAddress, userAgent);

        // 重定向到前端，携带令牌信息
        const redirectUrl = this.buildSuccessRedirectUrl(result);
        res.redirect(redirectUrl);

      } catch (error) {
        logger.error('微信OAuth回调处理失败', { error });
        this.redirectWithError(res, 'oauth_callback_error', '微信登录处理失败');
      }
    })(req, res, next);
  };

  /**
   * 发起微博OAuth登录
   */
  weiboAuth = passport.authenticate('weibo');

  /**
   * 微博OAuth回调处理
   */
  weiboCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    passport.authenticate('weibo', { session: false }, async (err, profile: OAuthProfile) => {
      try {
        if (err) {
          logger.error('微博OAuth认证失败', { error: err });
          return this.redirectWithError(res, 'oauth_error', '微博认证失败');
        }

        if (!profile) {
          logger.warn('微博OAuth认证被拒绝');
          return this.redirectWithError(res, 'oauth_denied', '微博认证被拒绝');
        }

        const ipAddress = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent');

        const result = await this.oauthService.handleOAuthLogin(profile, ipAddress, userAgent);

        // 重定向到前端，携带令牌信息
        const redirectUrl = this.buildSuccessRedirectUrl(result);
        res.redirect(redirectUrl);

      } catch (error) {
        logger.error('微博OAuth回调处理失败', { error });
        this.redirectWithError(res, 'oauth_callback_error', '微博登录处理失败');
      }
    })(req, res, next);
  };

  /**
   * 解除OAuth账户关联
   */
  disconnectProvider = async (req: Request, res: Response): Promise<void> => {
    try {
      const { provider } = req.params;
      const userId = (req as any).user?.userId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      if (!provider) {
        res.status(400).json({
          error: 'missing_provider',
          message: '缺少提供商参数'
        });
        return;
      }

      // 查找并解除关联
      const federatedIdentity = await this.oauthService.disconnectProvider(userId, provider);

      if (!federatedIdentity) {
        res.status(404).json({
          error: 'connection_not_found',
          message: '未找到该提供商的关联账户'
        });
        return;
      }

      res.status(200).json({
        message: '账户关联已解除',
        provider,
        disconnectedAt: new Date().toISOString()
      });

    } catch (error) {
      logger.error('解除OAuth关联失败', { error, provider: req.params.provider });
      res.status(500).json({
        error: 'disconnect_failed',
        message: '解除账户关联失败'
      });
    }
  };
}
