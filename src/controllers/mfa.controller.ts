/**
 * MFA控制器
 * 处理多因素认证相关的HTTP请求
 */

import { Response } from 'express';
import { MfaService } from '@/services/mfa.service';
import { AuthenticatedRequest } from '@/middleware/auth.middleware';
import { logger } from '@/config/logger';
import { validateEnableMfaRequest, validateVerifyMfaRequest } from '@/utils/validation';

/**
 * MFA控制器类
 */
export class MfaController {
  private mfaService: MfaService;

  constructor() {
    this.mfaService = new MfaService();
  }

  /**
   * 获取用户MFA状态
   */
  getMfaStatus = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      const devices = await this.mfaService.getUserMfaDevices(userId);
      const isEnabled = devices.length > 0;
      const methods = devices.map(device => device.type);

      res.status(200).json({
        isEnabled,
        methods,
        devices: devices.map(device => ({
          id: device.id,
          type: device.type,
          name: device.name,
          isVerified: device.isVerified,
          lastUsedAt: device.lastUsedAt,
          createdAt: device.createdAt
        }))
      });

    } catch (error) {
      logger.error('获取MFA状态失败', { error, userId: req.user?.userId });
      
      res.status(500).json({
        error: 'mfa_status_failed',
        message: '获取MFA状态失败'
      });
    }
  };

  /**
   * 启用MFA
   */
  enableMfa = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      // 验证请求数据
      const validationResult = validateEnableMfaRequest(req.body);
      if (!validationResult.isValid) {
        res.status(400).json({
          error: 'validation_error',
          message: '请求数据验证失败',
          details: validationResult.errors
        });
        return;
      }

      const { method, name, phoneNumber, emailAddress } = req.body;

      let result;

      switch (method) {
        case 'totp':
          result = await this.mfaService.enableTotp({
            userId,
            method,
            name
          });
          
          res.status(200).json({
            setupKey: result.secret,
            qrCodeUri: result.qrCodeUri,
            backupCodes: result.backupCodes,
            message: '请使用认证器应用扫描二维码，然后验证以完成设置'
          });
          break;

        case 'email':
          result = await this.mfaService.enableEmail({
            userId,
            method,
            name,
            emailAddress
          });
          
          res.status(200).json({
            deviceId: result.id,
            message: '邮件MFA已启用，请验证以完成设置'
          });
          break;

        case 'sms':
          result = await this.mfaService.enableSms({
            userId,
            method,
            name,
            phoneNumber
          });
          
          res.status(200).json({
            deviceId: result.id,
            message: '短信MFA已启用，请验证以完成设置'
          });
          break;

        default:
          res.status(400).json({
            error: 'invalid_method',
            message: '不支持的MFA方法'
          });
          return;
      }

    } catch (error) {
      logger.error('启用MFA失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'mfa_enable_failed',
        message: (error as Error).message || '启用MFA失败'
      });
    }
  };

  /**
   * 验证MFA
   */
  verifyMfa = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const sessionId = req.user?.sessionId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      // 验证请求数据
      const validationResult = validateVerifyMfaRequest(req.body);
      if (!validationResult.isValid) {
        res.status(400).json({
          error: 'validation_error',
          message: '请求数据验证失败',
          details: validationResult.errors
        });
        return;
      }

      const { deviceId, code } = req.body;

      const result = await this.mfaService.verifyMfa({
        userId,
        deviceId,
        code,
        sessionId
      });

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'MFA验证成功'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'mfa_verification_failed',
          message: 'MFA验证失败',
          remainingAttempts: result.remainingAttempts
        });
      }

    } catch (error) {
      logger.error('MFA验证失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'mfa_verification_failed',
        message: (error as Error).message || 'MFA验证失败'
      });
    }
  };

  /**
   * 禁用MFA设备
   */
  disableMfaDevice = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const { deviceId } = req.params;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      if (!deviceId) {
        res.status(400).json({
          error: 'missing_device_id',
          message: '缺少设备ID'
        });
        return;
      }

      await this.mfaService.disableMfaDevice(userId, deviceId);

      res.status(204).send();

    } catch (error) {
      logger.error('禁用MFA设备失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'mfa_disable_failed',
        message: (error as Error).message || '禁用MFA设备失败'
      });
    }
  };

  /**
   * 发送邮件验证码
   */
  sendEmailCode = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const { deviceId } = req.body;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      if (!deviceId) {
        res.status(400).json({
          error: 'missing_device_id',
          message: '缺少设备ID'
        });
        return;
      }

      await this.mfaService.sendEmailCode(userId, deviceId);

      res.status(200).json({
        message: '验证码已发送到您的邮箱'
      });

    } catch (error) {
      logger.error('发送邮件验证码失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'send_email_code_failed',
        message: (error as Error).message || '发送邮件验证码失败'
      });
    }
  };

  /**
   * 发送短信验证码
   */
  sendSmsCode = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const { deviceId } = req.body;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      if (!deviceId) {
        res.status(400).json({
          error: 'missing_device_id',
          message: '缺少设备ID'
        });
        return;
      }

      await this.mfaService.sendSmsCode(userId, deviceId);

      res.status(200).json({
        message: '验证码已发送到您的手机'
      });

    } catch (error) {
      logger.error('发送短信验证码失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'send_sms_code_failed',
        message: (error as Error).message || '发送短信验证码失败'
      });
    }
  };
}
