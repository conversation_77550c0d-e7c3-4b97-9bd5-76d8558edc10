/**
 * 应用程序入口文件
 * 启动Express服务器和初始化应用
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import passport from 'passport';
import { config } from '@/config';
import { logger } from '@/config/logger';
import { connectDatabase, checkDatabaseHealth } from '@/config/database';
import authRoutes from '@/routes/auth.routes';
import userRoutes from '@/routes/user.routes';
import oauthRoutes from '@/routes/oauth.routes';
import gatewayRoutes from '@/routes/gateway.routes';
import jwksRoutes from '@/routes/jwks.routes';
import '@/config/passport'; // 初始化Passport策略

/**
 * 创建Express应用
 */
const app = express();

/**
 * 安全中间件配置
 */
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

/**
 * CORS配置
 */
app.use(cors({
  origin: function (origin, callback) {
    // 允许的源列表
    const allowedOrigins = [
      config.server.frontendUrl,
      'http://localhost:3001',
      'http://localhost:3000'
    ];

    // 开发环境允许所有源
    if (config.server.nodeEnv === 'development') {
      return callback(null, true);
    }

    // 检查源是否在允许列表中
    if (!origin || allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    callback(new Error('不允许的CORS源'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

/**
 * 基础中间件
 */
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 初始化Passport
app.use(passport.initialize());

/**
 * 信任代理（用于获取真实IP）
 */
app.set('trust proxy', 1);

/**
 * 请求日志中间件
 */
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('HTTP请求', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  });
  
  next();
});

/**
 * 健康检查端点
 */
app.get('/health', async (_req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbHealth ? 'connected' : 'disconnected',
      memory: process.memoryUsage(),
      version: process.version
    };

    res.status(dbHealth ? 200 : 503).json(health);
  } catch (error) {
    logger.error('健康检查失败', { error });
    res.status(503).json({
      status: 'error',
      message: '服务不可用'
    });
  }
});

/**
 * API路由
 */
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/auth', oauthRoutes); // OAuth路由
app.use('/api/v1/me', userRoutes);
app.use('/api/v1/gateway', gatewayRoutes); // 网关集成路由

// JWKS和OpenID Connect发现端点
app.use('/', jwksRoutes);

// 非标准应用路由
import { nonStandardAppRoutes, initializeServices } from './routes/non-standard-app.routes';
app.use('/nsa', nonStandardAppRoutes);

/**
 * 根路径
 */
app.get('/', (_req, res) => {
  res.json({
    name: 'Identity Provider (IdP)',
    version: '1.0.0',
    description: '身份提供商 - 统一身份认证和授权服务',
    endpoints: {
      health: '/health',
      auth: '/api/v1/auth',
      docs: '/api/docs'
    }
  });
});

/**
 * 404处理
 */
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'not_found',
    message: '请求的资源不存在',
    path: req.originalUrl
  });
});

/**
 * 全局错误处理中间件
 */
app.use((error: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logger.error('未处理的错误', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  // 不泄露内部错误信息到客户端
  if (config.server.nodeEnv === 'production') {
    res.status(500).json({
      error: 'internal_server_error',
      message: '服务器内部错误'
    });
  } else {
    res.status(500).json({
      error: 'internal_server_error',
      message: error.message,
      stack: error.stack
    });
  }
});

/**
 * 启动服务器
 */
async function startServer(): Promise<void> {
  try {
    // 连接数据库
    await connectDatabase();
    logger.info('数据库连接成功');

    // 初始化非标准应用服务
    try {
      await initializeServices();
      logger.info('非标准应用服务初始化完成');
    } catch (error) {
      logger.error('非标准应用服务初始化失败', { error });
    }

    // 启动HTTP服务器
    const server = app.listen(config.server.port, () => {
      logger.info('服务器启动成功', {
        port: config.server.port,
        nodeEnv: config.server.nodeEnv,
        pid: process.pid
      });

      console.log(`🚀 身份提供商服务运行在端口 ${config.server.port}`);
      console.log(`📖 API文档: http://localhost:${config.server.port}/api/docs`);
      console.log(`🔍 健康检查: http://localhost:${config.server.port}/health`);
      console.log(`🔧 非标准应用API: http://localhost:${config.server.port}/nsa`);
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      logger.info(`收到${signal}信号，开始优雅关闭...`);
      
      server.close(() => {
        logger.info('HTTP服务器已关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 未捕获异常处理
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常', { error });
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝', { reason, promise });
      process.exit(1);
    });

  } catch (error) {
    logger.error('服务器启动失败', { error });
    process.exit(1);
  }
}

// 启动应用
if (require.main === module) {
  startServer().catch((error) => {
    logger.error('应用启动失败', { error });
    process.exit(1);
  });
}

export default app;
