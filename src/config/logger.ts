/**
 * 日志配置
 * 使用Winston进行结构化日志记录
 */

import winston from 'winston';
import path from 'path';

// 日志级别配置
const logLevel = process.env.LOG_LEVEL || 'info';
const logFilePath = process.env.LOG_FILE_PATH || './logs/app.log';

// 确保日志目录存在
const logDir = path.dirname(logFilePath);

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta
    });
  })
);

// 控制台输出格式（开发环境）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// 创建Winston logger实例
export const logger = winston.createLogger({
  level: logLevel,
  format: logFormat,
  defaultMeta: { service: 'id-provider' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 所有日志文件
    new winston.transports.File({
      filename: logFilePath,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// 生产环境错误处理
if (process.env.NODE_ENV === 'production') {
  logger.exceptions.handle(
    new winston.transports.File({ 
      filename: path.join(logDir, 'exceptions.log'),
      maxsize: 5242880,
      maxFiles: 5,
    })
  );
}

/**
 * 创建子logger，用于特定模块
 * @param module 模块名称
 * @returns 子logger实例
 */
export function createModuleLogger(module: string): winston.Logger {
  return logger.child({ module });
}

/**
 * 记录安全事件
 * @param event 安全事件类型
 * @param details 事件详情
 * @param userId 用户ID（可选）
 * @param ipAddress IP地址（可选）
 */
export function logSecurityEvent(
  event: string,
  details: Record<string, any>,
  userId?: string,
  ipAddress?: string
): void {
  logger.warn('安全事件', {
    event,
    details,
    userId,
    ipAddress,
    timestamp: new Date().toISOString()
  });
}

/**
 * 记录审计事件
 * @param action 操作类型
 * @param resource 操作资源
 * @param userId 用户ID
 * @param details 操作详情
 * @param success 操作是否成功
 */
export function logAuditEvent(
  action: string,
  resource: string,
  userId: string,
  details: Record<string, any>,
  success: boolean = true
): void {
  logger.info('审计事件', {
    action,
    resource,
    userId,
    details,
    success,
    timestamp: new Date().toISOString()
  });
}

/**
 * 记录性能指标
 * @param operation 操作名称
 * @param duration 执行时间（毫秒）
 * @param details 额外详情
 */
export function logPerformance(
  operation: string,
  duration: number,
  details?: Record<string, any>
): void {
  logger.info('性能指标', {
    operation,
    duration,
    details,
    timestamp: new Date().toISOString()
  });
}

export default logger;
