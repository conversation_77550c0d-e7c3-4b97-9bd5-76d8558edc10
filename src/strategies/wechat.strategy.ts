/**
 * 微信OAuth策略
 * 实现微信开放平台的OAuth 2.0认证
 */

import { BaseOAuthStrategy, OAuthConfig, OAuthUserInfo } from './base-oauth.strategy';
import { config } from '@/config';
import { logger } from '@/config/logger';

/**
 * 微信用户信息接口
 */
interface WeChatUserInfo {
  openid: string;
  nickname?: string;
  sex?: number;
  province?: string;
  city?: string;
  country?: string;
  headimgurl?: string;
  unionid?: string;
}

/**
 * 微信OAuth策略类
 */
export class WeChatStrategy extends BaseOAuthStrategy {
  constructor(verify: Function) {
    const wechatConfig: OAuthConfig = {
      clientId: config.oauth.wechat.appId!,
      clientSecret: config.oauth.wechat.appSecret!,
      callbackUrl: config.oauth.wechat.callbackUrl,
      authorizationUrl: 'https://open.weixin.qq.com/connect/qrconnect',
      tokenUrl: 'https://api.weixin.qq.com/sns/oauth2/access_token',
      userInfoUrl: 'https://api.weixin.qq.com/sns/userinfo',
      scope: ['snsapi_login']
    };

    super('wechat', wechatConfig, verify);
  }

  /**
   * 重写获取用户信息方法，适配微信API
   */
  protected async getUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    try {
      // 微信需要先获取openid
      const tokenInfo = await this.getTokenInfo(accessToken);
      
      if (!tokenInfo.openid) {
        throw new Error('无法获取微信用户openid');
      }

      // 使用openid和access_token获取用户信息
      const userInfoUrl = `${this.config.userInfoUrl}?access_token=${accessToken}&openid=${tokenInfo.openid}&lang=zh_CN`;
      
      const response = await fetch(userInfoUrl);
      const wechatUser: WeChatUserInfo = await response.json();

      if ((wechatUser as any).errcode) {
        throw new Error(`微信API错误: ${(wechatUser as any).errmsg}`);
      }

      // 转换为标准格式
      const userInfo: OAuthUserInfo = {
        id: wechatUser.unionid || wechatUser.openid,
        name: wechatUser.nickname,
        avatar: wechatUser.headimgurl
      };

      logger.info('微信用户信息获取成功', { 
        openid: wechatUser.openid,
        unionid: wechatUser.unionid 
      });

      return userInfo;

    } catch (error) {
      logger.error('获取微信用户信息失败', { error });
      throw new Error('获取微信用户信息失败');
    }
  }

  /**
   * 获取令牌信息（包含openid）
   */
  private async getTokenInfo(accessToken: string): Promise<any> {
    try {
      // 微信的access_token响应中包含openid
      // 这里需要从之前的令牌交换响应中获取
      // 实际实现中应该缓存这个信息
      return { openid: 'cached_openid' }; // 简化实现
    } catch (error) {
      logger.error('获取微信令牌信息失败', { error });
      throw error;
    }
  }

  /**
   * 重写令牌交换方法，适配微信API
   */
  protected async exchangeCodeForToken(code: string): Promise<any> {
    try {
      const tokenUrl = `${this.config.tokenUrl}?appid=${this.config.clientId}&secret=${this.config.clientSecret}&code=${code}&grant_type=authorization_code`;
      
      const response = await fetch(tokenUrl);
      const tokenData = await response.json();

      if (tokenData.errcode) {
        throw new Error(`微信令牌交换失败: ${tokenData.errmsg}`);
      }

      return {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        openid: tokenData.openid,
        unionid: tokenData.unionid
      };

    } catch (error) {
      logger.error('微信令牌交换失败', { error });
      throw new Error('微信令牌交换失败');
    }
  }
}
