/**
 * 基础OAuth策略
 * 为自定义OAuth提供商提供通用的OAuth 2.0实现
 */

import { Strategy } from 'passport-strategy';
import { Request } from 'express';
import { logger } from '@/config/logger';

/**
 * OAuth配置接口
 */
export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  callbackUrl: string;
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scope?: string[];
}

/**
 * OAuth用户信息接口
 */
export interface OAuthUserInfo {
  id: string;
  email?: string;
  name?: string;
  avatar?: string;
  [key: string]: any;
}

/**
 * 基础OAuth策略类
 */
export class BaseOAuthStrategy extends Strategy {
  name: string;
  protected config: OAuthConfig;

  constructor(name: string, config: OAuthConfig, verify: Function) {
    super();
    this.name = name;
    this.config = config;
    this._verify = verify;
  }

  /**
   * 认证方法
   */
  authenticate(req: Request, options?: any): void {
    if (req.query.error) {
      // OAuth错误处理
      const error = new Error(`OAuth认证失败: ${req.query.error_description || req.query.error}`);
      return this.fail(error.message);
    }

    if (req.query.code) {
      // 处理回调，交换授权码获取访问令牌
      this.handleCallback(req);
    } else {
      // 重定向到授权页面
      this.redirectToAuthorizationUrl(req);
    }
  }

  /**
   * 重定向到授权URL
   */
  private redirectToAuthorizationUrl(req: Request): void {
    const state = this.generateState();
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: this.config.callbackUrl,
      response_type: 'code',
      state,
      ...(this.config.scope && { scope: this.config.scope.join(' ') })
    });

    const authUrl = `${this.config.authorizationUrl}?${params.toString()}`;
    this.redirect(authUrl);
  }

  /**
   * 处理OAuth回调
   */
  private async handleCallback(req: Request): Promise<void> {
    try {
      const { code, state } = req.query;

      if (!code) {
        return this.fail('缺少授权码');
      }

      // 交换访问令牌
      const tokenResponse = await this.exchangeCodeForToken(code as string);
      
      if (!tokenResponse.access_token) {
        return this.fail('获取访问令牌失败');
      }

      // 获取用户信息
      const userInfo = await this.getUserInfo(tokenResponse.access_token);

      // 调用验证回调
      this._verify(
        tokenResponse.access_token,
        tokenResponse.refresh_token,
        userInfo,
        (err: any, user: any) => {
          if (err) {
            return this.error(err);
          }
          if (!user) {
            return this.fail('用户验证失败');
          }
          return this.success(user);
        }
      );

    } catch (error) {
      logger.error(`${this.name} OAuth回调处理失败`, { error });
      this.error(error);
    }
  }

  /**
   * 交换授权码获取访问令牌
   */
  protected async exchangeCodeForToken(code: string): Promise<any> {
    try {
      const tokenData = new URLSearchParams({
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        code,
        redirect_uri: this.config.callbackUrl,
        grant_type: 'authorization_code'
      });

      const response = await fetch(this.config.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        body: tokenData
      });

      const result = await response.json();
      return result;
    } catch (error) {
      logger.error(`${this.name} 令牌交换失败`, { error });
      throw new Error('令牌交换失败');
    }
  }

  /**
   * 获取用户信息
   */
  protected async getUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    try {
      const response = await fetch(this.config.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      logger.error(`${this.name} 获取用户信息失败`, { error });
      throw new Error('获取用户信息失败');
    }
  }

  /**
   * 生成状态参数
   */
  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * 验证回调函数
   */
  private _verify: Function;
}
